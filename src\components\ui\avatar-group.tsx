
import * as React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "./avatar"
import { cn } from "@/lib/utils"

interface AvatarGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  items: {
    image?: string
    fallback: string
    tooltip?: string
  }[]
  limit?: number
}

export function AvatarGroup({
  items,
  limit = 4,
  className,
  ...props
}: AvatarGroupProps) {
  const itemsToShow = items.slice(0, limit)
  const remaining = items.length - limit
  
  return (
    <div
      className={cn("flex -space-x-2", className)}
      {...props}
    >
      {itemsToShow.map((item, index) => (
        <Avatar key={index} className="border-2 border-background">
          {item.image && <AvatarImage src={item.image} alt={item.fallback} />}
          <AvatarFallback className="bg-muted">{item.fallback}</AvatarFallback>
        </Avatar>
      ))}
      {remaining > 0 && (
        <Avatar className="border-2 border-background">
          <AvatarFallback className="bg-primary text-primary-foreground">
            +{remaining}
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  )
}
