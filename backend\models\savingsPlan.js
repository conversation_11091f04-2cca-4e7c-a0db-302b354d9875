const mongoose = require('mongoose');

const SavingsPlanSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
  },
  depositFrequency: {
    type: String,
    enum: ['Daily', 'Weekly', 'Bi-weekly', 'Monthly', 'Yearly', 'daily', 'weekly', 'bi-weekly', 'monthly', 'yearly'],
    required: true,
  },
  depositAmount: {
    type: Number,
    required: true,
  },
  targetDate: {
    type: Date,
    required: true,
  },
  targetAmount: {
    type: Number,
    required: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  savedAmount: {
    type: Number,
    default: 0,
  },
  lastDepositDate: {
    type: Date,
    default: null,
  },
  // Track when interest was last applied
  lastInterestApplied: {
    type: Date,
    default: null,
  },
});

module.exports = mongoose.model('SavingsPlan', SavingsPlanSchema);