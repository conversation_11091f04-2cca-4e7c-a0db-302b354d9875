import {
  BarChart3,
  CreditCard,
  DollarSign,
  PiggyBank,
  PlusCircle,
  Send,
  TrendingUp,
  Clock,
  Target,
  CalendarDays,
  ArrowUpRight,
  Percent,
  ArrowRight,
  Wallet,
  Gift,
  Sparkles,
  Users,
  Eye,
  EyeOff,
  Upload,
} from "lucide-react";
import { StatCard } from "@/components/ui/stat-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AvatarGroup } from "@/components/ui/avatar-group";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { PaymentProofUpload } from "@/components/payments/PaymentProofUpload";
import { useBalance } from "@/hooks/use-balance";
import { PayoutModeDialog } from "@/components/payments/PayoutModeDialog";
import { QuickPayoutDialog } from "@/components/payments/QuickPayoutDialog";

const recentTransactions = [
  {
    id: "t1",
    description: "Daily Deposit",
    amount: 5000,
    date: "Today",
    type: "deposit",
  },
  {
    id: "t2", 
    description: "Withdrawal",
    amount: -2000,
    date: "Yesterday",
    type: "withdrawal",
  },
  {
    id: "t3",
    description: "Bonus Credit",
    amount: 1000,
    date: "May 21, 2023",
    type: "deposit",
  },
  {
    id: "t4",
    description: "Daily Deposit",
    amount: 5000,
    date: "May 20, 2023",
    type: "deposit",
  },
];

const userData = {
  name: "Ade",
  phone: "8105551234",
};

const UserDashboard = () => {
  const navigate = useNavigate();
  const [showBalance, setShowBalance] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showPayoutModeModal, setShowPayoutModeModal] = useState(false);
  const [showPayoutModal, setShowPayoutModal] = useState(false);
  const { balance, isLoading: balanceLoading } = useBalance();

  const [transactions, setTransactions] = useState(recentTransactions);

  // Enhanced stats with modern design
  const enhancedStats = [
    {
      title: "Total Balance",
      value: showBalance ? `₦${balance.toLocaleString()}` : "••••••",
      change: "+12.5%",
      changeType: "increase",
      icon: <Wallet className="h-5 w-5" />,
      color: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20"
    },
    {
      title: "This Month",
      value: showBalance ? "₦85,640" : "••••••",
      change: "+8.2%",
      changeType: "increase", 
      icon: <TrendingUp className="h-5 w-5" />,
      color: "from-blue-500 to-cyan-600",
      bgGradient: "from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20"
    },
    {
      title: "Savings Goal",
      value: "73%",
      change: "+5.1%",
      changeType: "increase",
      icon: <Target className="h-5 w-5" />,
      color: "from-purple-500 to-violet-600", 
      bgGradient: "from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20"
    },
    {
      title: "Interest Earned",
      value: showBalance ? "₦12,340" : "••••••",
      change: "+15.3%",
      changeType: "increase",
      icon: <Percent className="h-5 w-5" />,
      color: "from-orange-500 to-red-500",
      bgGradient: "from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20"
    }
  ];

  useEffect(() => {
    const savedTransactions = localStorage.getItem('user_transactions');
    if (savedTransactions) {
      const parsedTransactions = JSON.parse(savedTransactions);
      const combinedTransactions = [...parsedTransactions, ...recentTransactions]
        .slice(0, 4);
      setTransactions(combinedTransactions);
    }
  }, []);

  const handlePaymentProofSubmit = (formData) => {
    console.log("Payment proof submitted:", formData);
    toast.success("Payment proof submitted successfully! We'll verify your payment soon.");
    setShowPaymentModal(false);
  };

  const handleConfigurePayoutSettings = () => {
    setShowPayoutModal(false);
    setShowPayoutModeModal(true);
  };

  return (
    <div className="space-y-6 p-4 lg:p-6">
      {/* Welcome Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8" data-aos="fade-down">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Good Morning, {userData.name}! 👋
          </h1>
          <p className="text-muted-foreground mt-1">Your financial overview at a glance</p>
        </div>
        <div className="flex items-center gap-3 mt-4 lg:mt-0">
          <Button variant="outline" size="sm" onClick={() => setShowBalance(!showBalance)}>
            {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {showBalance ? "Hide" : "Show"}
          </Button>
          <Button onClick={() => setShowPaymentModal(true)} className="gap-2">
            <PlusCircle className="h-4 w-4" />
            Add Money
          </Button>
        </div>
      </div>

      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 lg:gap-6" data-aos="fade-up" data-aos-delay="100">
        {enhancedStats.map((stat, index) => (
          <Card 
            key={index} 
            className={`relative overflow-hidden border-0 bg-gradient-to-br ${stat.bgGradient} hover:shadow-lg transition-all duration-300 hover:scale-105`}
            data-aos="zoom-in"
            data-aos-delay={100 + index * 50}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs font-medium text-green-600">{stat.change}</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full bg-gradient-to-r ${stat.color} text-white shadow-lg`}>
                  {stat.icon}
                </div>
              </div>
              {/* Decorative gradient overlay */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-10 translate-x-10" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions & Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6" data-aos="fade-up" data-aos-delay="200">
        
        {/* Quick Actions - Inspired by the reference dashboard */}
        <div className="xl:col-span-1">
          <Card className="border-0 bg-gradient-to-br from-card to-accent/5 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={() => navigate('/savings')} 
                className="w-full justify-start gap-3 h-12 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-md"
                data-aos="slide-right"
                data-aos-delay="300"
              >
                <PiggyBank className="h-4 w-4" />
                Start New Savings Plan
              </Button>
              <Button 
                onClick={() => setShowPaymentModal(true)}
                variant="outline" 
                className="w-full justify-start gap-3 h-12 border-primary/20 hover:bg-primary/5"
                data-aos="slide-right" 
                data-aos-delay="350"
              >
                <PlusCircle className="h-4 w-4" />
                Add Money
              </Button>
              <Button 
                onClick={() => setShowPayoutModal(true)}
                variant="outline"
                className="w-full justify-start gap-3 h-12 border-orange-200 hover:bg-orange-50 text-orange-700"
                data-aos="slide-right"
                data-aos-delay="400"
              >
                <Send className="h-4 w-4" />
                Quick Withdrawal
              </Button>
              <Button 
                onClick={() => navigate('/analytics')}
                variant="ghost"
                className="w-full justify-start gap-3 h-12 hover:bg-blue-50 text-blue-700"
                data-aos="slide-right"
                data-aos-delay="450"
              >
                <BarChart3 className="h-4 w-4" />
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Area */}
        <div className="xl:col-span-2 space-y-6">
          
          {/* Activity Overview - Inspired by accounting dashboard */}
          <Card className="border-0 bg-gradient-to-br from-card to-muted/5 shadow-lg" data-aos="fade-left" data-aos-delay="300">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  Recent Activity
                </CardTitle>
                <CardDescription>Your latest financial activities</CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={() => navigate('/transactions')}>
                View All <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {transactions.slice(0, 4).map((transaction, index) => (
                <div 
                  key={transaction.id}
                  className="flex items-center justify-between p-3 rounded-lg bg-gradient-to-r from-background to-muted/5 border border-border/50 hover:shadow-md transition-all duration-200"
                  data-aos="slide-up"
                  data-aos-delay={350 + index * 50}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${
                      transaction.type === 'deposit' 
                        ? 'bg-green-100 text-green-600 dark:bg-green-900/20' 
                        : 'bg-red-100 text-red-600 dark:bg-red-900/20'
                    }`}>
                      {transaction.type === 'deposit' ? (
                        <ArrowUpRight className="h-4 w-4" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-muted-foreground">{transaction.date}</p>
                    </div>
                  </div>
                  <div className={`font-semibold ${
                    transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.amount > 0 ? '+' : ''}₦{Math.abs(transaction.amount).toLocaleString()}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Savings Goals Progress - Mini cards inspired by dashboard */}
          <Card className="border-0 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 shadow-lg" data-aos="fade-left" data-aos-delay="400">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-600" />
                Savings Goals Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 rounded-lg bg-white/70 dark:bg-background/70 border border-purple-200/50" data-aos="zoom-in" data-aos-delay="450">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-purple-700">Emergency Fund</span>
                    <span className="text-xs text-muted-foreground">73%</span>
                  </div>
                  <Progress value={73} className="h-2 mb-2" />
                  <p className="text-xs text-muted-foreground">₦73,000 of ₦100,000</p>
                </div>
                <div className="p-4 rounded-lg bg-white/70 dark:bg-background/70 border border-blue-200/50" data-aos="zoom-in" data-aos-delay="500">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-700">Vacation Fund</span>
                    <span className="text-xs text-muted-foreground">45%</span>
                  </div>
                  <Progress value={45} className="h-2 mb-2" />
                  <p className="text-xs text-muted-foreground">₦22,500 of ₦50,000</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Feature Highlight Card - Inspired by promotional sections */}
      <Card className="overflow-hidden border-0 bg-gradient-to-r from-primary/10 via-accent/10 to-secondary/10 shadow-lg" data-aos="fade-up" data-aos-delay="500">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-full bg-gradient-to-r from-primary to-primary/80 text-white shadow-lg">
                <Gift className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Better Interest Rates Available!</h3>
                <p className="text-sm text-muted-foreground">Upgrade to premium savings plans and earn up to 15% annually</p>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-md whitespace-nowrap">
              Learn More
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Dialogs */}
      <Dialog open={showPaymentModal} onOpenChange={setShowPaymentModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Submit Payment Proof</DialogTitle>
          </DialogHeader>
          <PaymentProofUpload 
            onSubmit={handlePaymentProofSubmit}
            isLoading={false}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showPayoutModeModal} onOpenChange={setShowPayoutModeModal}>
        <PayoutModeDialog onClose={() => setShowPayoutModeModal(false)} />
      </Dialog>

      <Dialog open={showPayoutModal} onOpenChange={setShowPayoutModal}>
        <QuickPayoutDialog 
          onClose={() => setShowPayoutModal(false)} 
          onConfigureSettings={handleConfigurePayoutSettings}
        />
      </Dialog>
    </div>
  );
};

export default UserDashboard;