import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { FintechCard } from '@/components/ui/fintech-card';
import FixedDepositForm from '@/components/savings/FixedDepositForm';
import FlexSavingsCard from '@/components/savings/FlexSavingsCard';
import SafeLockForm from '@/components/savings/SafeLockForm';
import AutoSaveSettings from '@/components/savings/AutoSaveSettings';
import { TrendingUp, Wallet, Shield, Repeat, Target, Coins } from 'lucide-react';

const SavingsPlans = () => {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Savings Plans</h1>
        <p className="text-muted-foreground">
          Choose the perfect savings plan to grow your wealth
        </p>
      </div>

      <Tabs defaultValue="flex" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="flex" className="flex items-center gap-2">
            <Wallet className="h-4 w-4" />
            <span className="hidden sm:inline">Flex</span>
          </TabsTrigger>
          <TabsTrigger value="fixed" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            <span className="hidden sm:inline">Fixed</span>
          </TabsTrigger>
          <TabsTrigger value="safelock" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">SafeLock</span>
          </TabsTrigger>
          <TabsTrigger value="target" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            <span className="hidden sm:inline">Target</span>
          </TabsTrigger>
          <TabsTrigger value="autosave" className="flex items-center gap-2">
            <Repeat className="h-4 w-4" />
            <span className="hidden sm:inline">AutoSave</span>
          </TabsTrigger>
          <TabsTrigger value="roundup" className="flex items-center gap-2">
            <Coins className="h-4 w-4" />
            <span className="hidden sm:inline">Round-Up</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="flex" className="space-y-6">
          <FlexSavingsCard />
        </TabsContent>

        <TabsContent value="fixed" className="space-y-6">
          <FixedDepositForm />
        </TabsContent>

        <TabsContent value="safelock" className="space-y-6">
          <SafeLockForm />
        </TabsContent>

        <TabsContent value="target" className="space-y-6">
          <FintechCard variant="glassmorphic" className="p-6">
            <div className="text-center py-12">
              <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Target Savings</h3>
              <p className="text-muted-foreground">
                Set specific goals and save towards them systematically.
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Coming soon...
              </p>
            </div>
          </FintechCard>
        </TabsContent>

        <TabsContent value="autosave" className="space-y-6">
          <AutoSaveSettings />
        </TabsContent>

        <TabsContent value="roundup" className="space-y-6">
          <FintechCard variant="glassmorphic" className="p-6">
            <div className="text-center py-12">
              <Coins className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Round-Up Savings</h3>
              <p className="text-muted-foreground">
                Save spare change from every transaction automatically.
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Coming soon...
              </p>
            </div>
          </FintechCard>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SavingsPlans;