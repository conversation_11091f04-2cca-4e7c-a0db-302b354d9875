
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 142 76% 36%; /* Green primary */
    --primary-foreground: 0 0% 100%;
    --primary-glow: 142 76% 45%;

    --secondary: 142 30% 25%; /* Dark green for secondary */
    --secondary-foreground: 0 0% 100%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 142 50% 90%; /* Light green as accent */
    --accent-foreground: 240 5.9% 10%;
    
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142 76% 36%; /* Green as ring */

    --radius: 0.75rem;
  }

  .dark {
    --background: 220 26% 8%; /* Dark gradient base */
    --foreground: 0 0% 98%;

    --card: 220 20% 12%; /* Glassmorphic dark cards */
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 76% 36%; /* Green primary consistent */
    --primary-foreground: 0 0% 98%;
    --primary-glow: 142 76% 45%;

    --secondary: 142 30% 25%; /* Dark green consistent */
    --secondary-foreground: 0 0% 100%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 142 50% 30%;
    --accent-foreground: 0 0% 98%;
    
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142 76% 36%;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-sans;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer utilities {
  .glass-card {
    @apply backdrop-blur-md bg-white/70 border border-white/20 shadow-glass dark:bg-fintech-card/80 dark:border-white/10 dark:shadow-dark-glass;
  }
  
  .text-balance {
    text-wrap: balance;
  }
  
  .dark-transition {
    @apply transition-colors duration-300;
  }
  
  .fintech-gradient {
    @apply bg-gradient-to-br from-fintech-dark via-fintech-card to-fintech-dark;
  }
  
  .fintech-card-gradient {
    @apply bg-gradient-to-br from-fintech-card to-fintech-card/50 backdrop-blur-md border border-white/10;
  }
  
  .savings-goal-card {
    @apply p-4 rounded-lg border transition-all duration-300;
    @apply bg-gradient-to-br from-card to-accent/5;
    @apply dark:from-card dark:to-primary/5;
    @apply hover:shadow-lg hover:scale-[1.02];
    @apply border-border dark:border-border;
  }
  
  .savings-goal-text {
    @apply text-card-foreground dark:text-card-foreground;
  }
  
  .savings-goal-muted {
    @apply text-muted-foreground dark:text-muted-foreground;
  }
  
  /* Enhanced 3D Button Styles */
  .btn-3d {
    @apply relative overflow-hidden transition-all duration-300;
    @apply shadow-[inset_0_1px_0_rgba(255,255,255,0.2),inset_0_-1px_0_rgba(0,0,0,0.2),0_4px_12px_rgba(0,0,0,0.15)];
    @apply hover:shadow-[inset_0_3px_8px_rgba(0,0,0,0.3),inset_0_1px_0_rgba(255,255,255,0.1)];
    @apply active:shadow-[inset_0_5px_12px_rgba(0,0,0,0.4)];
    border: 2px solid hsl(var(--primary));
    border-radius: 30px;
    @apply bg-gradient-to-b from-primary via-primary to-primary/90;
    @apply hover:scale-[0.97] active:scale-[0.95];
  }
  
  /* Mobile responsiveness utilities */
  .mobile-safe-area {
    @apply px-4 sm:px-6 lg:px-8 xl:px-12;
  }
  
  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }
  
  .mobile-button {
    @apply w-full sm:w-auto min-h-[48px] lg:min-h-[52px] text-base sm:text-sm lg:text-base;
  }
  
  /* Enhanced responsive utilities */
  .responsive-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .responsive-text {
    @apply text-sm sm:text-base lg:text-lg;
  }
  
  .responsive-heading {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }
  
  .responsive-card {
    @apply p-4 sm:p-6 lg:p-8;
  }
}

@layer components {
  .nav-link {
    @apply relative px-4 py-2 flex items-center gap-2 rounded-md transition-all duration-300 hover:bg-primary/10;
  }
  
  .nav-link.active {
    @apply bg-primary/15 text-primary;
  }

  .dashboard-card {
    @apply rounded-xl p-6 border border-border bg-card shadow-sm transition-all duration-300 hover:shadow-kola dark:hover:shadow-dark-glass;
  }
  
  .stat-card {
    @apply rounded-xl p-5 border border-border bg-card text-card-foreground flex flex-col hover:shadow-kola transition-all duration-300 dark:hover:shadow-dark-glass;
  }
  
  .btn-brand {
    @apply bg-brand-blue text-white hover:bg-brand-blue/90 transition-all;
  }
  
  .btn-accent {
    @apply bg-brand-yellow text-foreground hover:bg-brand-yellow/90 transition-all;
  }

  .kola-card {
    @apply rounded-xl border border-border bg-gradient-to-br from-white to-green-50 shadow-kola transition-all duration-300 hover:shadow-glass-strong hover:-translate-y-1 dark:from-gray-900 dark:to-gray-800;
  }
  
  .asusu-gradient {
    @apply bg-gradient-to-br from-primary to-green-700 text-white;
  }
  
  .title-font {
    @apply font-sans tracking-wider uppercase;
  }
  
  .currency-symbol {
    @apply font-medium;
  }
}
