const express = require('express');
const router = express.Router();
const Withdraw = require('../models/withdraw');
const User = require('../models/user');
const Deposit = require('../models/deposit');
const Transaction = require('../models/transaction');
const Notification = require('../models/notification');
const { authenticateToken } = require('../middleware/authMiddleware');
const axios = require('axios');

// In-app withdrawal and plan closure (no bank account, no Paystack)
router.post('/plan', authenticateToken, async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const userId = req.user.id;
    const { planId, amount, notes, planMaturityDate } = req.body;
    if (!planId || !amount) {
      return res.status(400).json({ error: 'Plan ID and amount are required.' });
    }
    if (typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({ error: 'Amount must be a positive number.' });
    }
    // Fetch user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    // Try to find the plan in SavingsPlan, then targetSavings, then groupSavings, then rotationalSavings
    const SavingsPlan = require('../models/savingsPlan');
    let plan = await SavingsPlan.findById(planId);
    let planType = 'individual';
    if (!plan) {
      const TargetSavings = require('../models/targetSavings');
      plan = await TargetSavings.findById(planId);
      planType = 'target';
    }
    if (!plan) {
      const GroupSavings = require('../models/groupSavings');
      plan = await GroupSavings.findById(planId);
      planType = 'group';
    }
    if (!plan) {
      const RotationalSavings = require('../models/rotationalSavings');
      plan = await RotationalSavings.findById(planId);
      planType = 'rotational';
    }
    if (!plan || String(plan.userId) !== String(userId)) {
      return res.status(404).json({ error: 'Savings plan not found' });
    }
    // Check status/closed for all types
    if ((plan.status && plan.status === 'closed') || (plan.isClosed && plan.isClosed === true)) {
      return res.status(400).json({ error: 'Plan is already closed.' });
    }
    // Check balance for all types
    const planBalance = plan.balance !== undefined ? plan.balance : (plan.currentAmount !== undefined ? plan.currentAmount : plan.savedAmount);
    if (planBalance < amount) {
      return res.status(400).json({ error: 'Insufficient plan balance.' });
    }
    // Fetch global penalty from settings
    const Settings = require('../models/settings');
    let penaltySetting = await Settings.findOne({ key: 'withdrawalPenalty' });
    let penaltyPercent = penaltySetting ? penaltySetting.value : 3;
    // Calculate penalty (no penalty if today is 31st, plan matured, or plan is completed)
    const today = new Date();
    let isMatured = false;
    if (planMaturityDate) {
      const maturity = new Date(planMaturityDate);
      isMatured = today >= maturity;
    }
    // Check if plan is completed (robust: progress >= 100, status COMPLETED, or for target/group/rotational: progress, status, or plan is not ACTIVE)
    let isCompleted = false;
    const statusStr = plan.status ? plan.status.toString().toUpperCase() : '';
    if (
      (plan.progress !== undefined && plan.progress >= 100) ||
      statusStr === 'COMPLETED' ||
      statusStr === 'CLOSED' ||
      (planType === 'target' && (statusStr !== 'ACTIVE' || plan.isClosed === true)) ||
      (planType === 'group' && (statusStr !== 'ACTIVE' || plan.isGroupCompleted === true)) ||
      (planType === 'rotational' && (statusStr !== 'ACTIVE' || plan.isRotationalCompleted === true))
    ) {
      isCompleted = true;
    }
    let penaltyAmount = 0;
    if (today.getDate() !== 31 && !isMatured && !isCompleted) {
      penaltyAmount = Math.round((amount * penaltyPercent) / 100);
    }
    const netAmount = amount - penaltyAmount;
    // Delete/close the plan from the database
    if (planType === 'individual') {
      await SavingsPlan.deleteOne({ _id: planId });
    } else if (planType === 'target') {
      const TargetSavings = require('../models/targetSavings');
      await TargetSavings.deleteOne({ _id: planId });
    } else if (planType === 'group') {
      const GroupSavings = require('../models/groupSavings');
      await GroupSavings.deleteOne({ _id: planId });
    } else if (planType === 'rotational') {
      const RotationalSavings = require('../models/rotationalSavings');
      await RotationalSavings.deleteOne({ _id: planId });
    }
    // Credit user in-app balance
    user.balance += netAmount;
    await user.save();
    // Record withdrawal (status: success, in-app)
    const withdraw = new Withdraw({
      userId,
      amount,
      penalty: penaltyAmount,
      netAmount,
      notes,
      planId,
      status: 'success',
      type: 'in-app',
      processedAt: new Date(),
    });
    await withdraw.save();
    // Record transaction for user
    await Transaction.create({
      userId: user._id,
      type: 'withdrawal',
      amount: netAmount,
      description: notes || 'In-app withdrawal & plan closure',
      balanceAfter: user.balance,
      reference: `INAPP-${withdraw._id}`
    });
    // Record penalty as a transaction for admin tracking
    if (penaltyAmount > 0) {
      await Transaction.create({
        userId: user._id,
        type: 'withdrawal_penalty',
        amount: penaltyAmount,
        description: 'Penalty for early plan closure',
        balanceAfter: user.balance,
        reference: `PENALTY-${withdraw._id}`
      });
    }
    // Notify user
    await Notification.create({
      userId,
      type: 'withdrawal_success',
      title: 'Plan Closed & Savings Withdrawn',
      message: `Your plan has been closed and ₦${netAmount} credited to your in-app balance. Penalty: ₦${penaltyAmount}${penaltyAmount === 0 ? ' (No penalty applies on the 31st or at plan maturity)' : ''}`,
    });
    res.status(201).json({ withdraw, penaltyAmount, netAmount });
  } catch (err) {
    console.error('Error in /withdraw/plan:', err.message);
    res.status(500).json({ error: 'Failed to process in-app withdrawal', details: err.message });
  }
});

// Initiate Paystack withdrawal (payout)
router.post('/initiate', authenticateToken, async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const userId = req.user.id;
    const { amount, notes, bankAccountId, planMaturityDate } = req.body;
    if (!amount || !bankAccountId) {
      return res.status(400).json({ error: 'Amount and bank account are required.' });
    }
    if (typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({ error: 'Amount must be a positive number.' });
    }
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    if (user.balance < amount) {
      return res.status(400).json({ error: 'Insufficient balance for withdrawal.' });
    }

    // Fetch global penalty from settings
    const Settings = require('../models/settings');
    let penaltySetting = await Settings.findOne({ key: 'withdrawalPenalty' });
    let penaltyPercent = penaltySetting ? penaltySetting.value : 3;

    // Calculate penalty (no penalty if today is 31st or plan matured)
    const today = new Date();
    let isMatured = false;
    if (planMaturityDate) {
      const maturity = new Date(planMaturityDate);
      isMatured = today >= maturity;
    }
    let penaltyAmount = 0;
    if (today.getDate() !== 31 && !isMatured) {
      penaltyAmount = Math.round((amount * penaltyPercent) / 100);
    }
    const netAmount = amount - penaltyAmount;
    if (user.balance < amount) {
      return res.status(400).json({ error: 'Insufficient balance for withdrawal.' });
    }
    // Get bank account details
    const WithdrawAccount = require('../models/withdrawAccounts');
    const account = await WithdrawAccount.findById(bankAccountId);
    if (!account) {
      return res.status(404).json({ error: 'Withdrawal account not found' });
    }
    // Step 1: Resolve account number (optional, for extra validation)
    // Step 2: Create transfer recipient on Paystack
    const recipientRes = await axios.post(
      `${process.env.PAYSTACK_BASE_URL}/transferrecipient`,
      {
        type: 'nuban',
        name: account.accountName,
        account_number: account.accountNumber,
        bank_code: account.bankCode || '058', // Default to GTBank if not provided
        currency: 'NGN',
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    const recipientCode = recipientRes.data.data.recipient_code;
    // Step 3: Initiate transfer (send netAmount to user)
    const transferRes = await axios.post(
      `${process.env.PAYSTACK_BASE_URL}/transfer`,
      {
        source: 'balance',
        amount: Math.round(netAmount * 100),
        recipient: recipientCode,
        reason: notes || 'Withdrawal',
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    const transferCode = transferRes.data.data.transfer_code;
    // Create withdrawal record (pending)
    const withdraw = new Withdraw({
      userId,
      amount,
      penalty: penaltyAmount,
      netAmount,
      notes,
      bankAccountId,
      paystackTransferCode: transferCode,
      paystackStatus: 'pending',
      status: 'pending',
    });
    await withdraw.save();
    // Notify user
    await Notification.create({
      userId,
      type: 'withdrawal_requested',
      title: 'Withdrawal Requested',
      message: `Your withdrawal request of ₦${netAmount} is being processed. Penalty: ₦${penaltyAmount}${penaltyAmount === 0 ? ' (No penalty applies on the 31st or at plan maturity)' : ''}`,
    });
    // Record penalty as a transaction for admin tracking
    if (penaltyAmount > 0) {
      await Transaction.create({
        userId: user._id,
        type: 'withdrawal_penalty',
        amount: penaltyAmount,
        description: 'Penalty for early withdrawal',
        balanceAfter: user.balance, // or null if not deducted from user
        reference: `PENALTY-${withdraw._id}`
      });
    }
    res.status(201).json({ withdraw, transferCode, penaltyAmount, netAmount });
// Admin: Get penalty summary (total and list)
router.get('/penalty-summary', async (req, res) => {
  try {
    const Transaction = require('../models/transaction');
    // Total penalty collected
    const total = await Transaction.aggregate([
      { $match: { type: 'withdrawal_penalty' } },
      { $group: { _id: null, sum: { $sum: '$amount' } } }
    ]);
    // Recent penalty transactions
    const recent = await Transaction.find({ type: 'withdrawal_penalty' })
      .sort({ createdAt: -1 })
      .limit(20);
    res.json({ total: total[0]?.sum || 0, recent });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch penalty summary' });
  }
});
  } catch (err) {
    console.error('Error in /initiate:', err.response?.data || err.message);
    res.status(500).json({ error: 'Failed to initiate withdrawal', details: err.message });
  }
});
// Paystack webhook for transfer status
router.post('/webhook', async (req, res) => {
  try {
    const event = req.body;
    if (event.event === 'transfer.success') {
      const transferCode = event.data.transfer_code;
      const withdraw = await Withdraw.findOne({ paystackTransferCode: transferCode });
      if (withdraw && withdraw.status === 'pending') {
        withdraw.status = 'success';
        withdraw.paystackStatus = 'success';
        withdraw.processedAt = new Date();
        await withdraw.save();
        // Deduct user balance now
        const user = await User.findById(withdraw.userId);
        if (user) {
          user.balance -= withdraw.amount;
          await user.save();
        }
        // Create transaction record
        const Transaction = require('../models/transaction');
        await Transaction.create({
          userId: withdraw.userId,
          type: 'withdrawal',
          amount: withdraw.amount,
          description: withdraw.notes || 'Withdrawal',
          balanceAfter: user ? user.balance : 0,
          reference: transferCode,
        });
        // Notify user
        await Notification.create({
          userId: withdraw.userId,
          type: 'withdrawal_approved',
          title: 'Withdrawal Successful',
          message: `Your withdrawal of ₦${withdraw.amount} was successful.`,
        });
      }
    } else if (event.event === 'transfer.failed') {
      const transferCode = event.data.transfer_code;
      const withdraw = await Withdraw.findOne({ paystackTransferCode: transferCode });
      if (withdraw && withdraw.status === 'pending') {
        withdraw.status = 'failed';
        withdraw.paystackStatus = 'failed';
        withdraw.processedAt = new Date();
        await withdraw.save();
        // Notify user
        await Notification.create({
          userId: withdraw.userId,
          type: 'withdrawal_rejected',
          title: 'Withdrawal Failed',
          message: `Your withdrawal of ₦${withdraw.amount} failed. Please try again.`,
        });
      }
    }
    res.sendStatus(200);
  } catch (error) {
    console.error('Paystack withdrawal webhook error:', error.message);
    res.sendStatus(500);
  }
});

// Get all withdrawals for a user
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const withdrawals = await Withdraw.find({ userId })
      .sort({ createdAt: -1 })
      .populate('bankAccountId');
    res.json(withdrawals);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch withdrawals' });
  }
});

// Get all withdrawals (admin or for all users)
router.get('/all', async (req, res) => {
  try {
    const withdrawals = await Withdraw.find().sort({ createdAt: -1 }).populate('bankAccountId');
    res.json(withdrawals);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch all withdrawals' });
  }
});

// (Optional) Admin: Approve or reject a withdrawal
router.patch('/:id', async (req, res) => {
  try {
    const { status } = req.body;
    const withdraw = await Withdraw.findById(req.params.id);
    if (!withdraw) return res.status(404).json({ error: 'Withdrawal not found' });
    withdraw.status = status;
    if (status === 'approved') {
      withdraw.processedAt = new Date();
      // Deduct balance here if not already done
      const user = await User.findById(withdraw.userId);
      if (user && user.balance >= withdraw.amount) {
        user.balance -= withdraw.amount;
        await user.save();
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_approved',
        title: 'Withdrawal Approved',
        message: `Your withdrawal of ₦${withdraw.amount} has been approved.`,
      });
    } else if (status === 'rejected') {
      withdraw.processedAt = new Date();
      // Refund the amount to the user's balance
      const user = await User.findById(withdraw.userId);
      if (user) {
        user.balance += withdraw.amount;
        await user.save();
        // Create a Transaction record for the refund
        await Transaction.create({
          userId: user._id,
          type: 'withdrawal_refund',
          amount: withdraw.amount,
          description: 'Refund for rejected withdrawal',
          balanceAfter: user.balance,
          reference: `WD-REFUND-${Date.now()}`
        });
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_rejected',
        title: 'Withdrawal Rejected',
        message: `Your withdrawal of ₦${withdraw.amount} was rejected.`,
      });
    }
    await withdraw.save();
    res.json(withdraw);
  } catch (err) {
    res.status(500).json({ error: 'Failed to update withdrawal status' });
  }
});

// PUT: Update withdrawal status (admin)
router.put('/:id/status', async (req, res) => {
  try {
    const { status, notes } = req.body;
    const withdraw = await Withdraw.findById(req.params.id);
    if (!withdraw) return res.status(404).json({ error: 'Withdrawal not found' });
    withdraw.status = status;
    if (notes) withdraw.notes = notes;
    if (status === 'approved') {
      withdraw.processedAt = new Date();
      // Deduct balance here if not already done
      const user = await User.findById(withdraw.userId);
      if (user && user.balance >= withdraw.amount) {
        user.balance -= withdraw.amount;
        await user.save();
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_approved',
        title: 'Withdrawal Approved',
        message: `Your withdrawal of ₦${withdraw.amount} has been approved.`,
      });
    } else if (status === 'rejected') {
      withdraw.processedAt = new Date();
      // Refund the amount to the user's balance
      const user = await User.findById(withdraw.userId);
      if (user) {
        user.balance += withdraw.amount;
        await user.save();
        // Create a Transaction record for the refund
        await Transaction.create({
          userId: user._id,
          type: 'withdrawal_refund',
          amount: withdraw.amount,
          description: 'Refund for rejected withdrawal',
          balanceAfter: user.balance,
          reference: `WD-REFUND-${Date.now()}`
        });
      }
      await Notification.create({
        userId: withdraw.userId,
        type: 'withdrawal_rejected',
        title: 'Withdrawal Rejected',
        message: `Your withdrawal of ₦${withdraw.amount} was rejected.`,
      });
    }
    await withdraw.save();
    res.json(withdraw);
  } catch (err) {
    res.status(500).json({ error: 'Failed to update withdrawal status' });
  }
});

// Get pending withdrawals count for a user
router.get('/user/:userId/pending-count', async (req, res) => {
  try {
    const { userId } = req.params;
    const count = await Withdraw.countDocuments({ userId, status: 'pending' });
    res.json({ count });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch pending withdrawals count' });
  }
});

// Admin: Get count of all pending withdrawals
router.get('/pending-count', async (req, res) => {
  try {
    const count = await Withdraw.countDocuments({ status: 'pending' });
    res.json({ count });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch pending withdrawals count' });
  }
});

module.exports = router;
