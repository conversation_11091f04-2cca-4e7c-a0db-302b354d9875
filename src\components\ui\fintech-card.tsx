import * as React from "react";
import { cn } from "@/lib/utils";

interface FintechCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "glassmorphic" | "gradient";
  children: React.ReactNode;
}

const FintechCard = React.forwardRef<HTMLDivElement, FintechCardProps>(
  ({ className, variant = "default", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "rounded-xl border transition-all duration-300",
          {
            "bg-card text-card-foreground shadow-sm hover:shadow-md": variant === "default",
            "fintech-card-gradient shadow-lg hover:shadow-xl": variant === "glassmorphic",
            "bg-gradient-to-br from-primary/10 to-secondary/10 border-primary/20 shadow-lg hover:shadow-xl": variant === "gradient",
          },
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
FintechCard.displayName = "FintechCard";

export { FintechCard };