
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue, 
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Building2, CreditCard, User } from "lucide-react";
import { toast } from "sonner";

interface PayoutModeSettingsProps {
  onSubmit: (data: PayoutSettings) => void;
  isLoading?: boolean;
  defaultValues?: PayoutSettings;
  currencySymbol?: string; // Add this prop
}

export interface PayoutSettings {
  payoutMode: 'bank_transfer' | 'mobile_money' | 'paypal';
  bankName?: string;
  accountNumber?: string;
  accountName?: string;
  mobileNumber?: string;
  paypalEmail?: string;
}

export function PayoutModeSettings({ 
  onSubmit, 
  isLoading = false,
  defaultValues,
  currencySymbol = "₦" // Default to Naira symbol
}: PayoutModeSettingsProps) {
  const [settings, setSettings] = useState<PayoutSettings>(defaultValues || {
    payoutMode: 'bank_transfer',
    bankName: '',
    accountNumber: '',
    accountName: '',
  });

  const handleChange = (name: string, value: string) => {
    setSettings(prev => ({ ...prev, [name]: value }));
  };

  const handleModeChange = (value: 'bank_transfer' | 'mobile_money' | 'paypal') => {
    setSettings(prev => ({ ...prev, payoutMode: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate based on payout mode
    if (settings.payoutMode === 'bank_transfer') {
      if (!settings.bankName || !settings.accountNumber || !settings.accountName) {
        toast.error('Please fill in all bank details');
        return;
      }
    } else if (settings.payoutMode === 'mobile_money') {
      if (!settings.mobileNumber) {
        toast.error('Please enter your mobile money number');
        return;
      }
    } else if (settings.payoutMode === 'paypal') {
      if (!settings.paypalEmail) {
        toast.error('Please enter your PayPal email');
        return;
      }
    }
    
    onSubmit(settings);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="payoutMode">Preferred Payout Method</Label>
        <Select 
          value={settings.payoutMode} 
          onValueChange={(value: any) => handleModeChange(value)}
        >
          <SelectTrigger className="w-full border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30">
            <SelectValue placeholder="Select payout method" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
            <SelectItem value="mobile_money">Mobile Money</SelectItem>
            <SelectItem value="paypal">PayPal</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {settings.payoutMode === 'bank_transfer' && (
        <>
          <div className="space-y-2">
            <Label htmlFor="bankName" className="flex items-center gap-1">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              Bank Name
            </Label>
            <Input
              id="bankName"
              value={settings.bankName || ''}
              onChange={(e) => handleChange('bankName', e.target.value)}
              placeholder="Enter your bank name"
              className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="accountNumber" className="flex items-center gap-1">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              Account Number
            </Label>
            <Input
              id="accountNumber"
              value={settings.accountNumber || ''}
              onChange={(e) => handleChange('accountNumber', e.target.value)}
              placeholder="Enter your account number"
              className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="accountName" className="flex items-center gap-1">
              <User className="h-4 w-4 text-muted-foreground" />
              Account Name
            </Label>
            <Input
              id="accountName"
              value={settings.accountName || ''}
              onChange={(e) => handleChange('accountName', e.target.value)}
              placeholder="Enter account holder name"
              className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
            />
          </div>
        </>
      )}
      
      {settings.payoutMode === 'mobile_money' && (
        <div className="space-y-2">
          <Label htmlFor="mobileNumber">Mobile Money Number</Label>
          <Input
            id="mobileNumber"
            value={settings.mobileNumber || ''}
            onChange={(e) => handleChange('mobileNumber', e.target.value)}
            placeholder="Enter your mobile money number"
            className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          />
        </div>
      )}
      
      {settings.payoutMode === 'paypal' && (
        <div className="space-y-2">
          <Label htmlFor="paypalEmail">PayPal Email</Label>
          <Input
            id="paypalEmail"
            type="email"
            value={settings.paypalEmail || ''}
            onChange={(e) => handleChange('paypalEmail', e.target.value)}
            placeholder="Enter your PayPal email"
            className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          />
        </div>
      )}
      
      <Button 
        type="submit" 
        disabled={isLoading} 
        className="w-full shadow-yellow"
      >
        {isLoading ? "Saving..." : "Save Payout Settings"}
      </Button>
    </form>
  );
}
