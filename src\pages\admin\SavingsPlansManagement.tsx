
import React, { useState, useEffect } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { StatCard } from "@/components/ui/stat-card";
import { 
  ChevronDown, 
  Plus, 
  Edit, 
  Trash, 
  Users, 
  TrendingUp, 
  BarChart, 
  Clock,
  Percent,
  Wallet,
  MoreVertical,
  Eye,
  UserCheck
} from "lucide-react";

export default function SavingsPlansManagement() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [editFormOpen, setEditFormOpen] = useState(false);
  const [newPlanOpen, setNewPlanOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewType, setViewType] = useState<"plans" | "savers">("plans");
  const [userDetailsOpen, setUserDetailsOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    minimumAmount: "1000",
    durationDays: "30",
    interestRate: "5",
    isActive: true,
    category: "daily"
  });
  
  // Mock data - would come from API in real implementation
  const savingsPlans = [
    {
      id: "1",
      name: "Daily Saver",
      description: "Start saving with as little as ₦1,000 per day",
      minimumAmount: "1000",
      durationDays: "30",
      interestRate: "5",
      isActive: true,
      category: "daily",
      usersCount: 234,
      totalBalance: "4,300,000",
      createdAt: "2023-02-15"
    },
    {
      id: "2",
      name: "Weekly Target",
      description: "Achieve your goals with weekly savings",
      minimumAmount: "5000",
      durationDays: "90",
      interestRate: "8",
      isActive: true,
      category: "weekly",
      usersCount: 156,
      totalBalance: "7,890,000",
      createdAt: "2023-03-10"
    },
    {
      id: "3",
      name: "Premium Savings",
      description: "Higher returns for longer-term commitment",
      minimumAmount: "10000",
      durationDays: "180",
      interestRate: "10.5",
      isActive: false,
      category: "premium",
      usersCount: 78,
      totalBalance: "12,450,000",
      createdAt: "2023-04-22"
    }
  ];

  // Mock savers data
  const saversList = [
    {
      id: "1",
      userId: "USR-001-4352",
      name: "Adeola Johnson",
      email: "<EMAIL>",
      phone: "+234 ************",
      planId: "1",
      planName: "Daily Saver",
      startDate: "2023-06-10",
      endDate: "2023-07-10",
      amountSaved: "30,000",
      status: "active"
    },
    {
      id: "2",
      userId: "USR-002-7834",
      name: "Chinedu Okonkwo",
      email: "<EMAIL>",
      phone: "+234 ************",
      planId: "2",
      planName: "Weekly Target",
      startDate: "2023-05-15",
      endDate: "2023-08-13",
      amountSaved: "65,000",
      status: "active"
    },
    {
      id: "3",
      userId: "USR-003-9912",
      name: "Fatima Ibrahim",
      email: "<EMAIL>",
      phone: "+234 ************",
      planId: "1",
      planName: "Daily Saver",
      startDate: "2023-06-01",
      endDate: "2023-07-01",
      amountSaved: "28,000",
      status: "active"
    },
    {
      id: "4",
      userId: "USR-004-2256",
      name: "Emmanuel Adebayo",
      email: "<EMAIL>",
      phone: "+234 ************",
      planId: "3",
      planName: "Premium Savings",
      startDate: "2023-04-25",
      endDate: "2023-10-22",
      amountSaved: "180,000",
      status: "active"
    },
    {
      id: "5",
      userId: "USR-005-7723",
      name: "Ngozi Eze",
      email: "<EMAIL>",
      phone: "+234 ************",
      planId: "2",
      planName: "Weekly Target",
      startDate: "2023-05-20",
      endDate: "2023-08-18",
      amountSaved: "55,000",
      status: "inactive"
    }
  ];

  const statsData = [
    {
      title: "Total Plans",
      value: "12",
      description: "Active savings plans",
      icon: <Wallet />,
      trend: { value: 8, isPositive: true }
    },
    {
      title: "Total Users",
      value: "1,245",
      description: "Users enrolled in plans",
      icon: <Users />,
      trend: { value: 12, isPositive: true }
    },
    {
      title: "Interest Paid",
      value: "₦2.4M",
      description: "Last 30 days",
      icon: <Percent />,
      trend: { value: 3, isPositive: true }
    },
    {
      title: "Average Duration",
      value: "95 days",
      description: "Average savings period",
      icon: <Clock />,
      trend: { value: 5, isPositive: true }
    }
  ];

  const handleEditPlan = (plan: any) => {
    setSelectedPlan(plan);
    setFormData({
      name: plan.name,
      description: plan.description,
      minimumAmount: plan.minimumAmount,
      durationDays: plan.durationDays,
      interestRate: plan.interestRate,
      isActive: plan.isActive,
      category: plan.category || "daily"
    });
    setEditFormOpen(true);
  };
  
  const handleDeletePlan = (plan: any) => {
    setSelectedPlan(plan);
    setIsDeleteDialogOpen(true);
  };
  
  const confirmDeletePlan = () => {
    setIsLoading(true);
    
    // Mock API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Plan Deleted",
        description: `The plan "${selectedPlan.name}" has been deleted.`,
      });
      setIsDeleteDialogOpen(false);
    }, 1000);
  };
  
  const handleSavePlan = () => {
    setIsLoading(true);
    
    // Validate form
    if (!formData.name || !formData.description || !formData.minimumAmount) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      setIsLoading(false);
      return;
    }
    
    // Mock API call to save the plan
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Success",
        description: editFormOpen 
          ? `The plan "${formData.name}" has been updated.`
          : `New plan "${formData.name}" has been created.`,
      });
      
      // Close the appropriate dialog
      if (editFormOpen) {
        setEditFormOpen(false);
      } else {
        setNewPlanOpen(false);
      }
      
      // Reset form
      setFormData({
        name: "",
        description: "",
        minimumAmount: "1000",
        durationDays: "30",
        interestRate: "5",
        isActive: true,
        category: "daily"
      });
    }, 1500);
  };
  
  const handleNewPlan = () => {
    setFormData({
      name: "",
      description: "",
      minimumAmount: "1000",
      durationDays: "30",
      interestRate: "5",
      isActive: true,
      category: "daily"
    });
    setNewPlanOpen(true);
  };

  const viewUserDetails = (user: any) => {
    setSelectedUser(user);
    setUserDetailsOpen(true);
  };

  const filteredPlans = savingsPlans.filter(plan => 
    plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    plan.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredSavers = saversList.filter(saver => 
    saver.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    saver.userId.toLowerCase().includes(searchQuery.toLowerCase()) ||
    saver.planName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Plan Form Dialog content
  const PlanFormContent = () => (
    <>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="name" className="text-right">Name</Label>
          <Input
            id="name"
            className="col-span-3"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="description" className="text-right">Description</Label>
          <Input
            id="description"
            className="col-span-3"
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="category" className="text-right">Category</Label>
          <select
            id="category"
            className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={formData.category}
            onChange={(e) => setFormData({...formData, category: e.target.value})}
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="premium">Premium</option>
            <option value="custom">Custom</option>
          </select>
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="minimumAmount" className="text-right">Min Amount (₦)</Label>
          <Input
            id="minimumAmount"
            type="number"
            className="col-span-3"
            value={formData.minimumAmount}
            onChange={(e) => setFormData({...formData, minimumAmount: e.target.value})}
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="durationDays" className="text-right">Duration (days)</Label>
          <Input
            id="durationDays"
            type="number"
            className="col-span-3"
            value={formData.durationDays}
            onChange={(e) => setFormData({...formData, durationDays: e.target.value})}
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="interestRate" className="text-right">Interest Rate (%)</Label>
          <Input
            id="interestRate"
            type="number"
            step="0.1"
            className="col-span-3"
            value={formData.interestRate}
            onChange={(e) => setFormData({...formData, interestRate: e.target.value})}
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="isActive" className="text-right">Status</Label>
          <div className="flex items-center space-x-2 col-span-3">
            <Switch 
              id="isActive" 
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData({...formData, isActive: checked})}
            />
            <Label htmlFor="isActive" className="cursor-pointer">
              {formData.isActive ? "Active" : "Inactive"}
            </Label>
          </div>
        </div>
      </div>
    </>
  );

  return (
    <div className="container mx-auto max-w-7xl py-6 animate-fade-in">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Savings Plans Management</h1>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button 
            onClick={() => setViewType("plans")}
            variant={viewType === "plans" ? "default" : "outline"}
            className={viewType === "plans" ? "bg-brand-blue text-white" : ""}
            size="sm"
          >
            <Wallet className="mr-2 h-4 w-4" /> Plans
          </Button>
          <Button 
            onClick={() => setViewType("savers")} 
            variant={viewType === "savers" ? "default" : "outline"}
            className={viewType === "savers" ? "bg-brand-blue text-white" : ""}
            size="sm"
          >
            <Users className="mr-2 h-4 w-4" /> Savers
          </Button>
          {viewType === "plans" && (
            <Button 
              onClick={handleNewPlan} 
              className="hover:animate-yellow-pulse bg-brand-yellow text-foreground hover:bg-brand-yellow/90 hover:shadow-yellow transition-all"
              size="sm"
            >
              <Plus className="mr-2 h-4 w-4" /> Add Plan
            </Button>
          )}
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {statsData.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            description={stat.description}
            icon={stat.icon}
            trend={stat.trend}
            className="animate-scale-in hover:shadow-yellow hover:-translate-y-1 transition-all duration-300"
          />
        ))}
      </div>
      
      {viewType === "plans" ? (
        <Tabs defaultValue="all" className="mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
            <TabsList className="bg-muted/50">
              <TabsTrigger value="all">All Plans</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
            </TabsList>
            
            <div className="w-full sm:w-auto">
              <Input
                placeholder="Search plans..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full sm:w-[250px]"
              />
            </div>
          </div>
          
          <TabsContent value="all" className="mt-0">
            <Card className="shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Plan Name</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Min. Amount</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Interest Rate</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Users</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPlans.length > 0 ? (
                        filteredPlans.map((plan) => (
                          <TableRow key={plan.id} className="hover:bg-brand-yellow/5 transition-colors">
                            <TableCell className="font-medium">
                              <div>
                                <div>{plan.name}</div>
                                <div className="text-xs text-muted-foreground">{plan.description}</div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="capitalize">
                                {plan.category}
                              </Badge>
                            </TableCell>
                            <TableCell>₦{parseInt(plan.minimumAmount).toLocaleString()}</TableCell>
                            <TableCell>{plan.durationDays} days</TableCell>
                            <TableCell>{plan.interestRate}%</TableCell>
                            <TableCell>
                              <Badge variant={plan.isActive ? "default" : "secondary"} className={plan.isActive ? "bg-green-500" : "bg-gray-500"}>
                                {plan.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </TableCell>
                            <TableCell>{plan.usersCount}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreVertical className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => handleEditPlan(plan)}>
                                    <Edit className="mr-2 h-4 w-4" /> Edit Plan
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleDeletePlan(plan)}>
                                    <Trash className="mr-2 h-4 w-4" /> Delete Plan
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Users className="mr-2 h-4 w-4" /> View Users
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <BarChart className="mr-2 h-4 w-4" /> View Stats
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-6">
                            No savings plans found matching your search.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="active" className="mt-0">
            <Card>
              <CardContent className="p-0">
                <Table>
                  {/* Similar table structure as "all" but filtered for active plans */}
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Min. Amount</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Interest Rate</TableHead>
                      <TableHead>Users</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPlans
                      .filter(plan => plan.isActive)
                      .map((plan) => (
                        <TableRow key={plan.id} className="hover:bg-brand-yellow/5">
                          <TableCell className="font-medium">
                            <div>
                              <div>{plan.name}</div>
                              <div className="text-xs text-muted-foreground">{plan.description}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {plan.category}
                            </Badge>
                          </TableCell>
                          <TableCell>₦{parseInt(plan.minimumAmount).toLocaleString()}</TableCell>
                          <TableCell>{plan.durationDays} days</TableCell>
                          <TableCell>{plan.interestRate}%</TableCell>
                          <TableCell>{plan.usersCount}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleEditPlan(plan)}>
                                  <Edit className="mr-2 h-4 w-4" /> Edit Plan
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDeletePlan(plan)}>
                                  <Trash className="mr-2 h-4 w-4" /> Delete Plan
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Users className="mr-2 h-4 w-4" /> View Users
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <BarChart className="mr-2 h-4 w-4" /> View Stats
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="inactive" className="mt-0">
            <Card>
              <CardContent className="p-0">
                <Table>
                  {/* Similar table structure as "all" but filtered for inactive plans */}
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Min. Amount</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Interest Rate</TableHead>
                      <TableHead>Users</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPlans
                      .filter(plan => !plan.isActive)
                      .map((plan) => (
                        <TableRow key={plan.id} className="hover:bg-brand-yellow/5">
                          <TableCell className="font-medium">
                            <div>
                              <div>{plan.name}</div>
                              <div className="text-xs text-muted-foreground">{plan.description}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {plan.category}
                            </Badge>
                          </TableCell>
                          <TableCell>₦{parseInt(plan.minimumAmount).toLocaleString()}</TableCell>
                          <TableCell>{plan.durationDays} days</TableCell>
                          <TableCell>{plan.interestRate}%</TableCell>
                          <TableCell>{plan.usersCount}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleEditPlan(plan)}>
                                  <Edit className="mr-2 h-4 w-4" /> Edit Plan
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDeletePlan(plan)}>
                                  <Trash className="mr-2 h-4 w-4" /> Delete Plan
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Users className="mr-2 h-4 w-4" /> View Users
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <BarChart className="mr-2 h-4 w-4" /> View Stats
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        // Savers List View
        <Card className="shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
          <CardHeader className="pb-0">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
              <CardTitle className="text-lg font-semibold">Savers List</CardTitle>
              <div className="w-full sm:w-auto mt-2 sm:mt-0">
                <Input
                  placeholder="Search savers by name, ID or plan..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full sm:w-[300px]"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Plan Type</TableHead>
                    <TableHead className="hidden md:table-cell">Start Date</TableHead>
                    <TableHead className="hidden md:table-cell">Amount Saved</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSavers.length > 0 ? (
                    filteredSavers.map((saver) => (
                      <TableRow key={saver.id} className="hover:bg-brand-blue/5 transition-colors">
                        <TableCell className="font-medium">{saver.userId}</TableCell>
                        <TableCell>{saver.name}</TableCell>
                        <TableCell>
                          <Badge className="bg-brand-blue text-white">
                            {saver.planName}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">{saver.startDate}</TableCell>
                        <TableCell className="hidden md:table-cell">₦{saver.amountSaved}</TableCell>
                        <TableCell>
                          <Badge variant={saver.status === "active" ? "default" : "secondary"} 
                                className={saver.status === "active" ? "bg-green-500" : "bg-gray-500"}>
                            {saver.status === "active" ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white"
                            onClick={() => viewUserDetails(saver)}
                          >
                            <Eye className="h-4 w-4 mr-1" /> View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-6">
                        No savers found matching your search.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Edit Plan Dialog */}
      <Dialog open={editFormOpen} onOpenChange={setEditFormOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Savings Plan</DialogTitle>
            <DialogDescription>
              Make changes to the savings plan.
            </DialogDescription>
          </DialogHeader>
          
          <PlanFormContent />
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditFormOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSavePlan} disabled={isLoading} className="bg-brand-blue hover:bg-brand-blue/90">
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* New Plan Dialog */}
      <Dialog open={newPlanOpen} onOpenChange={setNewPlanOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Savings Plan</DialogTitle>
            <DialogDescription>
              Enter the details for the new savings plan.
            </DialogDescription>
          </DialogHeader>
          
          <PlanFormContent />
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setNewPlanOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSavePlan} 
              disabled={isLoading} 
              className="bg-brand-yellow text-foreground hover:bg-brand-yellow/90 hover:shadow-yellow"
            >
              {isLoading ? "Creating..." : "Create Plan"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this savings plan? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDeletePlan} 
              disabled={isLoading}
            >
              {isLoading ? "Deleting..." : "Delete Plan"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* User Details Dialog */}
      <Dialog open={userDetailsOpen} onOpenChange={setUserDetailsOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>Saver Details</DialogTitle>
            <DialogDescription>
              Complete information about this saver's account.
            </DialogDescription>
          </DialogHeader>
          
          {selectedUser && (
            <div className="grid gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground text-xs">User ID</Label>
                  <p className="font-medium">{selectedUser.userId}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground text-xs">Name</Label>
                  <p className="font-medium">{selectedUser.name}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground text-xs">Email</Label>
                  <p className="font-medium">{selectedUser.email}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground text-xs">Phone</Label>
                  <p className="font-medium">{selectedUser.phone}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground text-xs">Plan</Label>
                  <p className="font-medium">{selectedUser.planName}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground text-xs">Status</Label>
                  <Badge variant={selectedUser.status === "active" ? "default" : "secondary"} 
                        className={selectedUser.status === "active" ? "bg-green-500" : "bg-gray-500"}>
                    {selectedUser.status === "active" ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div>
                  <Label className="text-muted-foreground text-xs">Start Date</Label>
                  <p className="font-medium">{selectedUser.startDate}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground text-xs">End Date</Label>
                  <p className="font-medium">{selectedUser.endDate}</p>
                </div>
              </div>
              
              <div className="mt-2">
                <Label className="text-muted-foreground text-xs">Amount Saved</Label>
                <p className="text-2xl font-bold text-brand-blue">₦{selectedUser.amountSaved}</p>
              </div>
              
              <div className="flex justify-between items-center mt-4">
                <Button variant="outline" size="sm">View Transactions</Button>
                <Button className="bg-brand-blue text-white hover:bg-brand-blue/90">Contact Saver</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
