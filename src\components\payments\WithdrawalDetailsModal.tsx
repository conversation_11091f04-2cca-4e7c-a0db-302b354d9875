
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  User,
  Calendar,
  CreditCard,
  Building2,
  FileText 
} from "lucide-react";

interface WithdrawalRequest {
  id: string;
  userId: string;
  userName: string;
  amount: number;
  date: string;
  status: "pending" | "approved" | "rejected";
  bankName: string;
  accountNumber: string;
  accountName: string;
  reason?: string;
}

interface WithdrawalDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  request: WithdrawalRequest | null;
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
}

export function WithdrawalDetailsModal({
  isOpen,
  onClose,
  request,
  onApprove,
  onReject,
}: WithdrawalDetailsModalProps) {
  if (!request) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Withdrawal Request Details</DialogTitle>
          <DialogDescription>
            Review the withdrawal request details before approving or rejecting.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Request Information</div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Date:</span>
              </div>
              <div>{request.date}</div>
              
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>User:</span>
              </div>
              <div>{request.userName}</div>
              
              <div className="flex items-center">
                <CreditCard className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Amount:</span>
              </div>
              <div className="font-semibold">₦{request.amount.toLocaleString()}</div>
              
              <div className="flex items-center">
                <FileText className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Status:</span>
              </div>
              <div>{request.status.charAt(0).toUpperCase() + request.status.slice(1)}</div>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Bank Details</div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center">
                <Building2 className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Bank:</span>
              </div>
              <div>{request.bankName}</div>
              
              <div className="flex items-center">
                <CreditCard className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Account Number:</span>
              </div>
              <div>{request.accountNumber}</div>
              
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Account Name:</span>
              </div>
              <div>{request.accountName}</div>
            </div>
          </div>
          
          {request.reason && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Reason for Withdrawal</div>
                <div className="rounded-md bg-muted p-3 text-sm">
                  {request.reason}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter className="sm:justify-between">
          {request.status === "pending" && (
            <div className="flex gap-2">
              <Button 
                variant="brand" 
                onClick={() => {
                  onApprove(request.id);
                  onClose();
                }}
              >
                <Check className="mr-2 h-4 w-4" />
                Approve
              </Button>
              <Button 
                variant="destructive" 
                onClick={() => {
                  onReject(request.id);
                  onClose();
                }}
              >
                <X className="mr-2 h-4 w-4" />
                Reject
              </Button>
            </div>
          )}
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Import Check and X icons
import { Check, X } from "lucide-react";
