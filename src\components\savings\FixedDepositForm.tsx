import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Calculator, Clock, TrendingUp } from 'lucide-react';

const FixedDepositForm = () => {
  const [amount, setAmount] = useState('');
  const [duration, setDuration] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const interestRates = {
    30: 12,
    60: 14,
    90: 16,
    180: 18,
    365: 20
  };

  const calculateReturns = () => {
    if (!amount || !duration) return 0;
    const principal = parseFloat(amount);
    const rate = interestRates[Number(duration) as keyof typeof interestRates] / 100;
    const days = parseInt(duration);
    return principal * rate * (days / 365);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Mock API call to create fixed deposit
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Fixed Deposit Created!",
        description: `Your ₦${parseFloat(amount).toLocaleString()} deposit has been locked for ${duration} days.`,
      });
      
      setAmount('');
      setDuration('');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create fixed deposit. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const returns = calculateReturns();

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <TrendingUp className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">Fixed Deposit</h2>
          <p className="text-sm text-muted-foreground">Lock funds for guaranteed returns</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <FloatingLabelInput
          id="amount"
          type="number"
          label="Amount (₦)"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          required
          min="10000"
          placeholder="10,000"
        />

        <div className="space-y-2">
          <label className="text-sm font-medium">Duration</label>
          <Select value={duration} onValueChange={setDuration}>
            <SelectTrigger>
              <SelectValue placeholder="Select duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="30">30 days (12% p.a.)</SelectItem>
              <SelectItem value="60">60 days (14% p.a.)</SelectItem>
              <SelectItem value="90">90 days (16% p.a.)</SelectItem>
              <SelectItem value="180">180 days (18% p.a.)</SelectItem>
              <SelectItem value="365">365 days (20% p.a.)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {amount && duration && (
          <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Calculator className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Projected Returns</span>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Principal:</span>
                <span>₦{parseFloat(amount).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Interest:</span>
                <span className="text-primary font-medium">₦{returns.toLocaleString()}</span>
              </div>
              <div className="flex justify-between font-semibold pt-2 border-t">
                <span>Total at Maturity:</span>
                <span>₦{(parseFloat(amount) + returns).toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}

        <Button 
          type="submit" 
          className="w-full" 
          disabled={loading || !amount || !duration}
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <span className="animate-spin mr-2">⟳</span> Creating Deposit...
            </span>
          ) : (
            <>
              <Clock className="mr-2 h-4 w-4" />
              Create Fixed Deposit
            </>
          )}
        </Button>
      </form>
    </FintechCard>
  );
};

export default FixedDepositForm;