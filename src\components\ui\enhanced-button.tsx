import React from "react";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "outline" | "accent";
  size?: "sm" | "md" | "lg";
}

export function EnhancedButton({ 
  children, 
  className, 
  variant = "default",
  size = "md",
  ...props 
}: EnhancedButtonProps) {
  return (
    <button
      className={cn(
        // Base 3D inward button styles
        "relative overflow-hidden transition-all duration-300 group",
        "shadow-[inset_0_3px_10px_rgba(0,0,0,0.1),0_2px_8px_rgba(0,0,0,0.1)]",
        "hover:shadow-[inset_0_5px_15px_rgba(0,0,0,0.15),0_4px_12px_rgba(0,0,0,0.15)]",
        "active:shadow-[inset_0_8px_20px_rgba(0,0,0,0.2)]",
        "border-[1.8px] border-[#FF8C00]", // Yellowish orange border
        "rounded-[30px]", // 30px border radius
        "bg-gradient-to-b from-white via-gray-50 to-gray-100",
        "dark:from-gray-700 dark:via-gray-800 dark:to-gray-900",
        "flex items-center justify-between gap-4",
        "hover:scale-[0.98] active:scale-[0.96]",
        "focus:outline-none focus:ring-2 focus:ring-primary/50",
        
        // Size variants
        size === "sm" && "px-4 py-2 text-sm",
        size === "md" && "px-6 py-3 text-base",
        size === "lg" && "px-8 py-4 text-lg",
        
        // Variant styles
        variant === "accent" && "bg-gradient-to-b from-primary/10 to-primary/20",
        variant === "outline" && "bg-transparent border-2",
        
        className
      )}
      {...props}
    >
      {/* Shimmer effect */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-[28px]" />
      </div>
      
      <span className="flex-1 text-left font-medium text-foreground dark:text-foreground relative z-10">
        {children}
      </span>
      
      {/* Circle with arrow */}
      <div className="relative z-10 w-8 h-8 rounded-full bg-[#FF8C00]/20 border border-[#FF8C00]/40 flex items-center justify-center group-hover:bg-[#FF8C00]/30 transition-colors">
        <ArrowRight className="w-4 h-4 text-[#FF8C00] group-hover:translate-x-0.5 transition-transform" />
      </div>
    </button>
  );
}