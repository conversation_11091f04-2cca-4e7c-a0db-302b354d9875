
/**
 * Utility functions to check network connectivity
 */

/**
 * Checks if the browser is currently online
 * @returns {boolean} - Whether the client has internet connectivity
 */
export const isOnline = (): boolean => {
  return typeof navigator !== 'undefined' && navigator.onLine;
};

/**
 * Waits for network connectivity before executing a callback
 * @param callback - Function to execute when online
 * @param timeout - Optional timeout in milliseconds
 * @returns {Promise<void>}
 */
export const waitForNetwork = (callback: () => void, timeout?: number): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isOnline()) {
      callback();
      resolve();
      return;
    }

    let timeoutId: NodeJS.Timeout | null = null;
    
    const handleOnline = () => {
      if (timeoutId) clearTimeout(timeoutId);
      window.removeEventListener('online', handleOnline);
      callback();
      resolve();
    };

    window.addEventListener('online', handleOnline);
    
    if (timeout) {
      timeoutId = setTimeout(() => {
        window.removeEventListener('online', handleOnline);
        reject(new Error('Network connection timeout'));
      }, timeout);
    }
  });
};
