
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Notification, NotificationType, useNotifications } from '@/hooks/use-notifications';
import { NotificationBadge } from '@/components/ui/notification-badge';
import { 
  Bell, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  X, 
  MailOpen,
  Smartphone,
  Trash2,
  CheckCheck,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'success':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'error':
      return <AlertCircle className="h-5 w-5 text-red-500" />;
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-amber-500" />;
    case 'info':
    default:
      return <Info className="h-5 w-5 text-blue-500" />;
  }
};

const getChannelIcon = (channel: string) => {
  switch (channel) {
    case 'email':
      return <MailOpen className="h-3 w-3" />;
    case 'sms':
      return <Smartphone className="h-3 w-3" />;
    case 'in-app':
      return <Bell className="h-3 w-3" />;
    case 'all':
    default:
      return (
        <div className="flex gap-1">
          <MailOpen className="h-3 w-3" />
          <Smartphone className="h-3 w-3" />
          <Bell className="h-3 w-3" />
        </div>
      );
  }
};

const getPriorityColor = (priority?: string) => {
  switch (priority) {
    case 'high':
      return 'text-red-500';
    case 'medium':
      return 'text-amber-500';
    case 'low':
    default:
      return 'text-green-500';
  }
};

const NotificationItem = ({ 
  notification, 
  onMarkAsRead,
  onDelete 
}: { 
  notification: Notification; 
  onMarkAsRead: () => void;
  onDelete: () => void;
}) => {
  const icon = getNotificationIcon(notification.type);
  const channelIcon = getChannelIcon(notification.channel);
  const priorityColor = getPriorityColor(notification.priority);
  
  const formattedTime = new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
    day: 'numeric',
    month: 'short',
  }).format(notification.timestamp);

  return (
    <div 
      className={cn(
        "p-3 border-b last:border-0 transition-colors",
        notification.read 
          ? "bg-transparent opacity-70 hover:bg-muted/30" 
          : "bg-muted/20 hover:bg-muted/40"
      )}
    >
      <div className="flex items-start gap-3">
        <div className="mt-1">{icon}</div>
        <div className="flex-1">
          <div className="flex justify-between items-center mb-1">
            <div className="flex items-center gap-2">
              <h4 className="font-unica-one tracking-wide text-sm">{notification.title}</h4>
              {notification.priority && (
                <span className={cn("text-xs", priorityColor)}>
                  {notification.priority}
                </span>
              )}
              {!notification.read && (
                <Badge variant="outline" className="bg-brand-blue text-white text-xs py-0 h-5">New</Badge>
              )}
            </div>
            <div className="flex gap-1">
              {!notification.read && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-6 w-6 p-0 hover:bg-green-100" 
                  onClick={(e) => {
                    e.stopPropagation();
                    onMarkAsRead();
                  }}
                  title="Mark as read"
                >
                  <CheckCheck className="h-4 w-4 text-green-600" />
                </Button>
              )}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-6 w-6 p-0 hover:bg-red-100" 
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                title="Delete notification"
              >
                <Trash2 className="h-4 w-4 text-red-500" />
              </Button>
            </div>
          </div>
          <p className="text-xs text-muted-foreground mb-2">{notification.message}</p>
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{formattedTime}</span>
            </div>
            <div className="flex items-center gap-1">
              {channelIcon}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export function NotificationCenter() {
  const { 
    notifications, 
    unreadCount, 
    markAsRead, 
    markAllAsRead, 
    clearNotifications,
    deleteNotification,
    filterNotifications
  } = useNotifications();
  const [open, setOpen] = useState(false);

  // Filter notifications by channel for different tabs
  const allNotifications = notifications;
  const inAppNotifications = filterNotifications(undefined, 'in-app');
  const emailNotifications = filterNotifications(undefined, 'email');
  const smsNotifications = filterNotifications(undefined, 'sms');

  // Filter by type
  const successNotifications = filterNotifications('success');
  const errorNotifications = filterNotifications('error');
  const warningNotifications = filterNotifications('warning');
  const infoNotifications = filterNotifications('info');

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="relative hover:bg-brand-yellow/20"
          title="Notifications"
        >
          <Bell className="h-5 w-5 text-brand-yellow" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1">
              <NotificationBadge count={unreadCount} />
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 md:w-96 p-0 border-brand-yellow/30" align="end">
        <div className="flex items-center justify-between p-4 border-b border-brand-yellow/20 bg-gradient-to-r from-brand-blue to-brand-deepBlue">
          <h3 className="font-unica-one tracking-wide text-lg text-brand-yellow">NOTIFICATIONS</h3>
          <div className="flex gap-2">
            {unreadCount > 0 && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={markAllAsRead} 
                className="h-8 text-xs border-brand-yellow/40 text-brand-yellow hover:bg-brand-yellow hover:text-brand-blue"
              >
                <CheckCheck className="h-3.5 w-3.5 mr-1" />
                Mark all read
              </Button>
            )}
            {notifications.length > 0 && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearNotifications} 
                className="h-8 text-xs border-brand-yellow/40 text-brand-yellow hover:bg-red-500 hover:text-white hover:border-transparent"
              >
                <Trash2 className="h-3.5 w-3.5 mr-1" />
                Clear all
              </Button>
            )}
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid grid-cols-4 w-full rounded-none border-b bg-muted/20">
            <TabsTrigger value="all" className="font-unica-one">ALL</TabsTrigger>
            <TabsTrigger value="in-app" className="font-unica-one">IN-APP</TabsTrigger>
            <TabsTrigger value="email" className="font-unica-one">EMAIL</TabsTrigger>
            <TabsTrigger value="sms" className="font-unica-one">SMS</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all">
            <NotificationList 
              notifications={allNotifications} 
              markAsRead={markAsRead}
              deleteNotification={deleteNotification}
              emptyMessage="No notifications" 
            />
          </TabsContent>
          
          <TabsContent value="in-app">
            <NotificationList 
              notifications={inAppNotifications} 
              markAsRead={markAsRead}
              deleteNotification={deleteNotification}
              emptyMessage="No in-app notifications" 
            />
          </TabsContent>
          
          <TabsContent value="email">
            <NotificationList 
              notifications={emailNotifications} 
              markAsRead={markAsRead}
              deleteNotification={deleteNotification}
              emptyMessage="No email notifications" 
            />
          </TabsContent>
          
          <TabsContent value="sms">
            <NotificationList 
              notifications={smsNotifications}
              markAsRead={markAsRead}
              deleteNotification={deleteNotification}
              emptyMessage="No SMS notifications" 
            />
          </TabsContent>
        </Tabs>
        
        <Separator />
        
        <div className="py-2 px-4 bg-muted/10">
          <h4 className="text-xs font-unica-one tracking-wide uppercase text-muted-foreground mb-2">Filter by type</h4>
          <div className="flex flex-wrap gap-2">
            <Badge 
              variant="outline" 
              className="flex items-center gap-1 cursor-pointer" 
              onClick={() => {}}
            >
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>Success ({successNotifications.length})</span>
            </Badge>
            <Badge 
              variant="outline" 
              className="flex items-center gap-1 cursor-pointer" 
              onClick={() => {}}
            >
              <AlertTriangle className="h-3 w-3 text-amber-500" />
              <span>Warning ({warningNotifications.length})</span>
            </Badge>
            <Badge 
              variant="outline" 
              className="flex items-center gap-1 cursor-pointer" 
              onClick={() => {}}
            >
              <AlertCircle className="h-3 w-3 text-red-500" />
              <span>Error ({errorNotifications.length})</span>
            </Badge>
            <Badge 
              variant="outline" 
              className="flex items-center gap-1 cursor-pointer" 
              onClick={() => {}}
            >
              <Info className="h-3 w-3 text-blue-500" />
              <span>Info ({infoNotifications.length})</span>
            </Badge>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

function NotificationList({ 
  notifications, 
  markAsRead,
  deleteNotification,
  emptyMessage 
}: { 
  notifications: Notification[]; 
  markAsRead: (id: string) => void;
  deleteNotification: (id: string) => void;
  emptyMessage: string; 
}) {
  if (notifications.length === 0) {
    return (
      <div className="py-12 px-4 text-center text-muted-foreground">
        <Bell className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
        <p className="font-unica-one tracking-wide">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-[350px]">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onMarkAsRead={() => markAsRead(notification.id)}
          onDelete={() => deleteNotification(notification.id)}
        />
      ))}
    </ScrollArea>
  );
}
