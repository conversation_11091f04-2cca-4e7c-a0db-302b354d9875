
import React from "react";
import { Trophy, Medal, Clock, Check } from "lucide-react";

interface SavingsRankBadgeProps {
  rank: string;
  duration: number; // in days
  consistencyScore: number;
}

export function SavingsRankBadge({ rank, duration, consistencyScore }: SavingsRankBadgeProps) {
  // Helper function to get badge color based on rank
  const getBadgeColor = () => {
    switch (rank.toLowerCase()) {
      case "bronze":
        return "bg-amber-700";
      case "silver":
        return "bg-gray-400";
      case "gold":
        return "bg-yellow-500";
      case "platinum":
        return "bg-slate-300";
      case "diamond":
        return "bg-cyan-400";
      default:
        return "bg-gray-400";
    }
  };

  // Format duration into readable format
  const formatDuration = (days: number) => {
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;
    
    if (months > 0) {
      return `${months} ${months === 1 ? 'month' : 'months'}${remainingDays > 0 ? ` ${remainingDays} ${remainingDays === 1 ? 'day' : 'days'}` : ''}`;
    }
    
    return `${days} ${days === 1 ? 'day' : 'days'}`;
  };

  return (
    <div className="flex flex-col items-center justify-center p-6 border rounded-lg bg-card text-card-foreground">
      <div className={`rounded-full p-4 ${getBadgeColor()} text-white mb-4`}>
        <Trophy className="h-12 w-12" />
      </div>
      
      <h3 className="text-xl font-bold mb-1">{rank} Saver</h3>
      
      <div className="space-y-3 mt-4 w-full">
        <div className="flex items-center text-sm">
          <Medal className="h-4 w-4 mr-2 text-brand-yellow" />
          <span>Rank: <strong>{rank}</strong></span>
        </div>
        
        <div className="flex items-center text-sm">
          <Clock className="h-4 w-4 mr-2 text-brand-blue" />
          <span>Saving for: <strong>{formatDuration(duration)}</strong></span>
        </div>
        
        <div className="flex items-center text-sm">
          <Check className="h-4 w-4 mr-2 text-green-500" />
          <span>Consistency: <strong>{consistencyScore}%</strong></span>
        </div>
      </div>
      
      {/* Progress to next rank */}
      <div className="w-full mt-4">
        <div className="flex justify-between text-xs mb-1">
          <span>Progress to next rank</span>
          <span>72%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-brand-blue h-2 rounded-full" style={{ width: "72%" }}></div>
        </div>
      </div>
    </div>
  );
}
