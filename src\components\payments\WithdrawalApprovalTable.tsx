
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Check, 
  X, 
  FileSearch, 
  AlertCircle,
  Calendar,
  User
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface WithdrawalRequest {
  id: string;
  userId: string;
  userName: string;
  amount: number;
  date: string;
  status: "pending" | "approved" | "rejected";
  bankName: string;
  accountNumber: string;
  accountName: string;
  reason?: string;
}

interface WithdrawalApprovalTableProps {
  withdrawalRequests: WithdrawalRequest[];
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
  onViewDetails: (request: WithdrawalRequest) => void;
}

export function WithdrawalApprovalTable({
  withdrawalRequests,
  onApprove,
  onReject,
  onViewDetails
}: WithdrawalApprovalTableProps) {
  const { toast } = useToast();
  const [processingId, setProcessingId] = useState<string | null>(null);

  const handleApprove = async (id: string) => {
    setProcessingId(id);
    try {
      await onApprove(id);
      toast({
        title: "Withdrawal Approved",
        description: "The withdrawal request has been approved successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve withdrawal. Please try again.",
        variant: "destructive",
      });
    } finally {
      setProcessingId(null);
    }
  };

  const handleReject = async (id: string) => {
    setProcessingId(id);
    try {
      await onReject(id);
      toast({
        title: "Withdrawal Rejected",
        description: "The withdrawal request has been rejected.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject withdrawal. Please try again.",
        variant: "destructive",
      });
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-500">Approved</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">Pending</Badge>;
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <FileSearch className="mr-2 h-5 w-5 text-brand-blue" />
          Withdrawal Requests
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative w-full overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b text-sm">
                <th className="py-3 px-2 text-left font-medium">User</th>
                <th className="py-3 px-2 text-left font-medium">Amount</th>
                <th className="py-3 px-2 text-left font-medium">Date</th>
                <th className="py-3 px-2 text-left font-medium">Status</th>
                <th className="py-3 px-2 text-left font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {withdrawalRequests.length === 0 ? (
                <tr>
                  <td colSpan={5} className="py-6 text-center text-muted-foreground">
                    <AlertCircle className="mx-auto mb-2 h-6 w-6" />
                    No withdrawal requests found
                  </td>
                </tr>
              ) : (
                withdrawalRequests.map((request) => (
                  <tr key={request.id} className="border-b hover:bg-muted/50 transition-colors">
                    <td className="py-3 px-2">
                      <div className="flex items-center">
                        <User className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>{request.userName}</span>
                      </div>
                    </td>
                    <td className="py-3 px-2 font-medium">
                      ₦{request.amount.toLocaleString()}
                    </td>
                    <td className="py-3 px-2 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        <span>{request.date}</span>
                      </div>
                    </td>
                    <td className="py-3 px-2">
                      {getStatusBadge(request.status)}
                    </td>
                    <td className="py-3 px-2">
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 px-2 text-xs"
                          onClick={() => onViewDetails(request)}
                        >
                          View
                        </Button>
                        {request.status === "pending" && (
                          <>
                            <Button
                              size="sm"
                              variant="brand"
                              className="h-8 px-2 text-xs"
                              onClick={() => handleApprove(request.id)}
                              disabled={processingId === request.id}
                            >
                              <Check className="h-3.5 w-3.5 mr-1" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              className="h-8 px-2 text-xs"
                              onClick={() => handleReject(request.id)}
                              disabled={processingId === request.id}
                            >
                              <X className="h-3.5 w-3.5 mr-1" />
                              Reject
                            </Button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
