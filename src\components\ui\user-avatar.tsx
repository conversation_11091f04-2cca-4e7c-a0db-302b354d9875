
import * as React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "./avatar"
import { cn } from "@/lib/utils"

interface UserAvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  user: {
    name?: string | null
    image?: string | null
  }
  className?: string
}

export function UserAvatar({ user, className, ...props }: UserAvatarProps) {
  const initials = React.useMemo(() => {
    if (!user.name) return "NN"
    return user.name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .substring(0, 2)
      .toUpperCase()
  }, [user.name])

  return (
    <Avatar className={cn("", className)} {...props}>
      {user.image ? (
        <AvatarImage src={user.image} alt={user.name || "User avatar"} />
      ) : (
        <AvatarFallback className="bg-primary text-primary-foreground">
          {initials}
        </AvatarFallback>
      )}
    </Avatar>
  )
}
