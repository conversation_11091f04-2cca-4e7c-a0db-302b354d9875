// backend/utils/interestRateUtil.js
const Settings = require('../models/settings');

// Helper to get interest rate for a plan type (falls back to global)
async function getInterestRate(planType) {
  // Try plan-specific
  const planKey = planType ? `interestRate_${planType}` : null;
  let rate = null;
  if (planKey) {
    const planSetting = await Settings.findOne({ key: planKey });
    if (planSetting && typeof planSetting.value === 'number') {
      rate = planSetting.value;
    }
  }
  if (rate === null) {
    // Fallback to global
    const globalSetting = await Settings.findOne({ key: 'globalInterestRate' });
    if (globalSetting && typeof globalSetting.value === 'number') {
      rate = globalSetting.value;
    } else {
      rate = 12; // Default 12% if not set
    }
  }
  return rate;
}

module.exports = { getInterestRate };
