
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Check } from "lucide-react";
import { OtpVerification } from "./OtpVerification";

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
}

export function PhoneInput({ value, onChange }: PhoneInputProps) {
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(true);

  const handleEdit = () => {
    setTempValue(value);
    setIsEditing(true);
  };

  const handleSave = () => {
    // Only initiate verification if phone number changed
    if (tempValue !== value) {
      onChange(tempValue);
      setIsVerified(false);
      setIsVerifying(true);
      toast({
        title: "Verification code sent",
        description: "A code has been sent to your new phone number",
      });
    } else {
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setTempValue(value);
  };

  const handleVerified = () => {
    setIsVerified(true);
    setIsVerifying(false);
    setIsEditing(false);
  };

  if (isVerifying) {
    return (
      <OtpVerification 
        phoneNumber={tempValue}
        onVerified={handleVerified}
        onCancel={() => {
          setIsVerifying(false);
          setIsEditing(false);
          setTempValue(value);
          onChange(value);
        }}
      />
    );
  }

  if (isEditing) {
    return (
      <div className="flex gap-2">
        <Input
          value={tempValue}
          onChange={(e) => setTempValue(e.target.value)}
          placeholder="+234 8XX XXX XXXX"
        />
        <Button
          onClick={handleSave}
          className="shrink-0 bg-brand-blue hover:bg-brand-blue/90"
        >
          Save
        </Button>
        <Button onClick={handleCancel} variant="outline" className="shrink-0">
          Cancel
        </Button>
      </div>
    );
  }

  return (
    <div className="flex gap-2 items-center">
      <Input value={value} readOnly />
      {isVerified && (
        <div className="bg-green-100 text-green-700 rounded-full p-1 shrink-0">
          <Check className="h-4 w-4" />
        </div>
      )}
      <Button
        onClick={handleEdit}
        variant="outline"
        size="sm"
        className="shrink-0"
      >
        Edit
      </Button>
    </div>
  );
}
