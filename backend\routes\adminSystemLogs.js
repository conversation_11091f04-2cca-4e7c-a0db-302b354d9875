const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');

// GET /api/admin/system-logs - Get system logs
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 50, search = '', level = '', service = '' } = req.query;
    
    // Mock system logs data - in a real implementation, this would come from a database
    const mockSystemLogs = [
      {
        _id: '1',
        level: 'error',
        message: 'Database connection failed',
        timestamp: new Date().toISOString(),
        service: 'database',
        details: { error: 'Connection timeout', retries: 3 },
        stack: 'Error: Connection timeout\n    at Database.connect (/app/database.js:45:12)',
        userId: req.user.id,
        userEmail: req.user.email,
        ipAddress: req.ip
      },
      {
        _id: '2',
        level: 'warn',
        message: 'High memory usage detected',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        service: 'system',
        details: { memoryUsage: '85%', threshold: '80%' },
        userId: req.user.id,
        userEmail: req.user.email,
        ipAddress: req.ip
      },
      {
        _id: '3',
        level: 'info',
        message: 'Payment webhook received',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        service: 'payment',
        details: { amount: 50000, status: 'success', provider: 'paystack' },
        userId: req.user.id,
        userEmail: req.user.email,
        ipAddress: req.ip
      },
      {
        _id: '4',
        level: 'debug',
        message: 'User authentication successful',
        timestamp: new Date(Date.now() - 10800000).toISOString(),
        service: 'auth',
        details: { userId: 'user123', method: 'jwt' },
        userId: req.user.id,
        userEmail: req.user.email,
        ipAddress: req.ip
      }
    ];

    // Filter logs based on search and filters
    let filteredLogs = mockSystemLogs;

    if (search) {
      filteredLogs = filteredLogs.filter(log =>
        log.message.toLowerCase().includes(search.toLowerCase()) ||
        log.service.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (level && level !== 'all') {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    if (service && service !== 'all') {
      filteredLogs = filteredLogs.filter(log => log.service === service);
    }

    // Pagination
    const pageNumber = parseInt(page);
    const limitNumber = parseInt(limit);
    const startIndex = (pageNumber - 1) * limitNumber;
    const endIndex = startIndex + limitNumber;
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    res.json({
      success: true,
      logs: paginatedLogs,
      pagination: {
        page: pageNumber,
        limit: limitNumber,
        total: filteredLogs.length,
        pages: Math.ceil(filteredLogs.length / limitNumber)
      }
    });
  } catch (error) {
    console.error('Error fetching system logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system logs'
    });
  }
});

// GET /api/admin/system-health - Get system health status
router.get('/health', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Mock system health data
    const systemHealth = {
      status: 'healthy',
      uptime: process.uptime(),
      memory: {
        used: process.memoryUsage().heapUsed,
        total: process.memoryUsage().heapTotal,
        percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
      },
      cpu: {
        usage: Math.random() * 100, // Mock CPU usage
        cores: require('os').cpus().length
      },
      database: {
        status: 'connected',
        responseTime: Math.random() * 100 + 10 // Mock response time
      },
      services: {
        api: 'online',
        database: 'online',
        cache: 'online',
        email: 'online'
      }
    };

    res.json({
      success: true,
      data: systemHealth
    });
  } catch (error) {
    console.error('Error fetching system health:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system health'
    });
  }
});

// GET /api/admin/system-logs/export - Export system logs
router.get('/export', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Mock system logs data for export
    const systemLogs = [
      {
        timestamp: new Date().toISOString(),
        level: 'error',
        message: 'Database connection failed',
        service: 'database',
        user: `${req.user.firstName} ${req.user.lastName}`,
        userEmail: req.user.email,
        ipAddress: req.ip
      }
    ];

    // Convert to CSV format
    const csvHeader = 'Timestamp,Level,Message,Service,User,User Email,IP Address\n';
    const csvRows = systemLogs.map(log => 
      `${log.timestamp},"${log.level}","${log.message}","${log.service}","${log.user}","${log.userEmail}","${log.ipAddress}"`
    ).join('\n');
    
    const csvContent = csvHeader + csvRows;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=system-logs-${new Date().toISOString().split('T')[0]}.csv`);
    res.send(csvContent);
  } catch (error) {
    console.error('Error exporting system logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export system logs'
    });
  }
});

module.exports = router; 