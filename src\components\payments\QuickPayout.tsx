
import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CircleDollarSign, Send } from "lucide-react";
import { toast } from "sonner";
import { useBalance } from "@/hooks/use-balance";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue, 
} from "@/components/ui/select";

interface QuickPayoutProps {
  onPayoutRequested: (amount: number, reason: string) => void;
  isLoading?: boolean;
}

export function QuickPayout({ onPayoutRequested, isLoading = false }: QuickPayoutProps) {
  const [amount, setAmount] = useState('');
  const [reason, setReason] = useState('general');
  const { balance, formattedBalance } = useBalance();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const numAmount = parseFloat(amount);
    
    if (isNaN(numAmount) || numAmount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }
    
    if (numAmount > balance) {
      toast.error('Insufficient balance');
      return;
    }

    onPayoutRequested(numAmount, reason);
    setAmount('');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-brand-lightBlue/20 dark:bg-brand-blue/20 p-4 rounded-lg border border-brand-blue/20 dark-transition">
        <div className="flex items-center gap-2">
          <CircleDollarSign className="h-5 w-5 text-brand-blue dark:text-brand-yellow" />
          <p className="font-medium">Available Balance: <span className="currency-symbol">{formattedBalance}</span></p>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="amount">Payout Amount (₦)</Label>
        <Input
          id="amount"
          type="number"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="Enter amount"
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30 dark:border-gray-700 dark:bg-gray-800"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="reason">Reason for Payout</Label>
        <Select 
          value={reason} 
          onValueChange={setReason}
        >
          <SelectTrigger className="w-full border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30 dark:border-gray-700 dark:bg-gray-800">
            <SelectValue placeholder="Select reason" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="general">General Withdrawal</SelectItem>
            <SelectItem value="savings_goal">Savings Goal Completion</SelectItem>
            <SelectItem value="emergency">Emergency Funds</SelectItem>
            <SelectItem value="investment">Investment Opportunity</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <Button 
        type="submit" 
        className="w-full bg-brand-blue hover:bg-brand-blue/90 dark:bg-brand-yellow dark:text-gray-900 dark:hover:bg-brand-yellow/90 flex items-center gap-2 dark-transition"
        disabled={isLoading}
      >
        <Send className="h-4 w-4" />
        {isLoading ? "Processing..." : "Request Payout"}
      </Button>
    </form>
  );
}
