import React from "react";
import { FintechCard } from "@/components/ui/fintech-card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { PiggyBank, Coins, Target, TrendingUp } from "lucide-react";

interface SavingsStatus {
  totalSaved: number;
  monthlyTarget: number;
  interestEarned: number;
  activePlans: number;
  monthlyProgress: number;
}

interface SavingsStatusProps {
  data: SavingsStatus;
  onCreatePlan?: () => void;
}

export function SavingsStatus({ data, onCreatePlan }: SavingsStatusProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const stats = [
    {
      icon: PiggyBank,
      label: "Total Saved",
      value: formatCurrency(data.totalSaved),
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
    },
    {
      icon: Coins,
      label: "Interest Earned",
      value: formatCurrency(data.interestEarned),
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/20",
    },
    {
      icon: Target,
      label: "Active Plans",
      value: data.activePlans.toString(),
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
    },
  ];

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="font-semibold">Savings Overview</h3>
        <Button size="sm" onClick={onCreatePlan}>
          <PiggyBank className="h-4 w-4 mr-1" />
          New Plan
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <Icon className={`h-5 w-5 ${stat.color}`} />
              </div>
              <div>
                <p className="text-xs text-muted-foreground">{stat.label}</p>
                <p className="font-semibold">{stat.value}</p>
              </div>
            </div>
          );
        })}
      </div>

      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Monthly Progress</span>
          </div>
          <span className="text-sm text-muted-foreground">
            {formatCurrency(data.monthlyProgress)} / {formatCurrency(data.monthlyTarget)}
          </span>
        </div>
        
        <Progress 
          value={(data.monthlyProgress / data.monthlyTarget) * 100} 
          className="h-2"
        />
        
        <p className="text-xs text-muted-foreground text-center">
          {((data.monthlyProgress / data.monthlyTarget) * 100).toFixed(1)}% of monthly target achieved
        </p>
      </div>

      <div className="mt-4 p-3 bg-primary/5 rounded-lg border border-primary/20">
        <div className="flex items-center gap-2 mb-1">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-green-700 dark:text-green-400">
            Earning Interest Daily
          </span>
        </div>
        <p className="text-xs text-muted-foreground">
          Your savings are growing automatically with daily compound interest
        </p>
      </div>
    </FintechCard>
  );
}