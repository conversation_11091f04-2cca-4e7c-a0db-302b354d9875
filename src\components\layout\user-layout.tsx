
import { useState, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { Sidebar } from "./sidebar";
import { UserNav } from "../user-nav";
import { <PERSON>u, X } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { useIsMobile } from "@/hooks/use-mobile";
import { ThemeToggle } from "../ui/theme-toggle";

interface UserLayoutProps {
  isAdmin?: boolean;
}

export function UserLayout({ isAdmin = false }: UserLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const isMobile = useIsMobile();
  const [compactMode, setCompactMode] = useState(false);
  const location = useLocation();

  // Close sidebar by default on mobile
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [location.pathname, isMobile]);

  return (
    <div className="flex min-h-screen fintech-gradient dark-transition">
      <div 
        className={`transition-all duration-300 fixed md:relative z-30 h-full ${
          sidebarOpen ? "block" : "hidden"
        } md:block`}
      >
        <Sidebar isAdmin={isAdmin} className={compactMode ? "w-16" : ""} collapsed={compactMode} />
      </div>
      
      <div className="flex-1 flex flex-col min-w-0 w-full">
        <header className="h-12 border-b border-white/10 flex items-center justify-between px-3 md:px-4 fintech-card-gradient sticky top-0 z-20 dark-transition">
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="icon" 
              className="md:hidden hover:bg-brand-yellow/20"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <X className="text-brand-blue h-4 w-4" /> : <Menu className="text-brand-blue h-4 w-4" />}
            </Button>
            <h1 className="text-base md:text-lg font-bold tracking-wider text-white">
              {isAdmin ? "ADMIN DASHBOARD" : "BETTER INTEREST"}
            </h1>
          </div>
          
          <div className="flex items-center gap-2 md:gap-3">
            <ThemeToggle />
            <div className="hidden md:flex items-center space-x-2">
              <Switch
                id="compact-mode"
                checked={compactMode}
                onCheckedChange={setCompactMode}
              />
              <Label htmlFor="compact-mode" className="text-xs font-unica-one">COMPACT</Label>
            </div>
            <UserNav isAdmin={isAdmin} />
          </div>
        </header>
        
        <main className={`flex-1 overflow-auto p-2 sm:p-4 md:p-6 animate-fade-in ${sidebarOpen && isMobile ? "blur-sm md:blur-none" : ""}`}>
          <div 
            className={`${sidebarOpen && isMobile ? "fixed inset-0 bg-black/20 z-10 md:hidden" : "hidden"}`} 
            onClick={() => setSidebarOpen(false)}
          />
          <Outlet />
        </main>
        
        <footer className="py-3 px-4 md:py-4 md:px-6 text-center text-xs md:text-sm text-muted-foreground border-t">
          © {new Date().getFullYear()} <span className="font-sans tracking-wider">Better Interest</span>. All rights reserved.
        </footer>
      </div>
    </div>
  );
}

export default UserLayout;
