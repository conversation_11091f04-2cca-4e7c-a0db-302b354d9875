const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');
const User = require('../models/user');

// POST /api/users/bulk-action - Perform bulk actions on users
router.post('/bulk-action', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userIds, action } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'User IDs array is required'
      });
    }

    if (!action || !['activate', 'deactivate', 'delete', 'promote', 'demote'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Valid action is required (activate, deactivate, delete, promote, demote)'
      });
    }

    let updateData = {};
    let message = '';

    switch (action) {
      case 'activate':
        updateData = { status: 'active' };
        message = 'Users activated successfully';
        break;
      case 'deactivate':
        updateData = { status: 'inactive' };
        message = 'Users deactivated successfully';
        break;
      case 'promote':
        updateData = { role: 'staff' };
        message = 'Users promoted to staff successfully';
        break;
      case 'demote':
        updateData = { role: 'user' };
        message = 'Users demoted to user successfully';
        break;
      case 'delete':
        // For delete, we'll actually delete the users
        await User.deleteMany({ _id: { $in: userIds } });
        return res.json({
          success: true,
          message: 'Users deleted successfully'
        });
    }

    // Update users
    const result = await User.updateMany(
      { _id: { $in: userIds } },
      updateData
    );

    res.json({
      success: true,
      message: message,
      updatedCount: result.modifiedCount
    });
  } catch (error) {
    console.error('Error performing bulk action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk action'
    });
  }
});

// POST /api/users/create-admin - Create admin user
router.post('/create-admin', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { firstName, lastName, email, phoneNumber, password, role } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email || !phoneNumber || !password) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Validate email format
    if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid email address'
      });
    }

    // Validate phone number
    if (!/^\d{11}$/.test(phoneNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Phone number must be 11 digits'
      });
    }

    // Validate password length
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters'
      });
    }

    // Check if email already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Email already in use'
      });
    }

    // Create new user
    const newUser = new User({
      firstName,
      lastName,
      email,
      phoneNumber,
      password,
      role: role || 'admin',
      status: 'active'
    });

    await newUser.save();

    res.status(201).json({
      success: true,
      message: `${role || 'Admin'} user created successfully`,
      user: {
        _id: newUser._id,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email,
        phoneNumber: newUser.phoneNumber,
        role: newUser.role,
        status: newUser.status
      }
    });
  } catch (error) {
    console.error('Error creating admin user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create admin user'
    });
  }
});

// GET /api/users/export - Export users data
router.get('/export', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await User.find({}).select('-password');

    // Convert to CSV format
    const csvHeader = 'ID,First Name,Last Name,Email,Phone Number,Role,Status,Created At\n';
    const csvRows = users.map(user => 
      `${user._id},"${user.firstName}","${user.lastName}","${user.email}","${user.phoneNumber || ''}","${user.role || 'user'}","${user.status || 'active'}","${user.createdAt}"`
    ).join('\n');
    
    const csvContent = csvHeader + csvRows;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=users-export-${new Date().toISOString().split('T')[0]}.csv`);
    res.send(csvContent);
  } catch (error) {
    console.error('Error exporting users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export users'
    });
  }
});

module.exports = router; 