
import { useState, useEffect } from 'react';
import { WifiOff, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { isOnline } from '@/utils/network';

export function OfflineIndicator() {
  const [isOffline, setIsOffline] = useState(!isOnline());

  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false);
      toast.success('You are back online');
    };
    
    const handleOffline = () => {
      setIsOffline(true);
      toast.error('You are offline. Some features may be limited.');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRefresh = () => {
    window.location.reload();
  };

  if (!isOffline) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-md shadow-lg flex items-center z-50">
      <WifiOff className="h-4 w-4 mr-2" />
      <span>You're offline. Some features may be limited.</span>
      <button 
        onClick={handleRefresh}
        className="ml-2 p-1 rounded-full hover:bg-red-400 transition-colors"
        aria-label="Refresh page"
      >
        <RefreshCw className="h-4 w-4" />
      </button>
    </div>
  );
}
