// Placeholder auth for build compatibility
export const signUp = async (email: string, password: string) => {
  return { data: null, error: null };
};

export const signIn = async (email: string, password: string) => {
  return { data: null, error: null };
};

export const signOut = async () => {
  return { error: null };
};

export const getCurrentUser = () => {
  return null;
};

export const uploadKycDocument = async (file: File, documentType: string) => {
  return { data: null, error: null };
};