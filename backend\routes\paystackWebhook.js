const express = require('express');
const router = express.Router();
const User = require('../models/user');

// Paystack webhook endpoint to update user balance
router.post('/paystack/webhook', async (req, res) => {
  try {
    const event = req.body;
    console.log('[Webhook] Received event:', JSON.stringify(event, null, 2));
    // Only handle successful charge events
    if (event.event === 'charge.success' && event.data && event.data.status === 'success') {
      const metadata = event.data.metadata || {};
      const userId = metadata.userId || metadata.user_id;
      const amount = event.data.amount;
      console.log('[Webhook] charge.success for userId:', userId, 'amount:', amount);
      if (!userId || !amount) {
        console.warn('[Webhook] Missing userId or amount in webhook payload:', { userId, amount });
        return res.status(400).json({ error: 'Missing userId or amount in webhook' });
      }
      const nairaAmount = amount / 100;
      // Log before update
      const userBefore = await User.findById(userId);
      console.log('[Webhook] User before update:', userBefore);
      // Update user balance atomically
      const user = await User.findByIdAndUpdate(
        userId,
        { $inc: { balance: nairaAmount } },
        { new: true }
      );
      if (!user) {
        console.error('[Webhook] User not found for update:', userId);
        return res.status(404).json({ error: 'User not found' });
      }
      console.log('[Webhook] User after update:', user);
      return res.status(200).json({ success: true, balance: user.balance });
    }
    // Ignore other events
    console.log('[Webhook] Ignored event type:', event.event);
    res.status(200).json({ received: true });
  } catch (err) {
    console.error('[Webhook] Error in webhook handler:', err);
    res.status(500).json({ error: err.message });
  }
});

module.exports = router;
