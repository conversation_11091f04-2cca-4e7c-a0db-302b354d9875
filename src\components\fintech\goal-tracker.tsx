import React from "react";
import { FintechCard } from "@/components/ui/fintech-card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Target } from "lucide-react";

interface SavingsGoal {
  id: string;
  title: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: string;
  color?: string;
}

interface GoalTrackerProps {
  goals: SavingsGoal[];
}

export function GoalTracker({ goals }: GoalTrackerProps) {
  const [currentIndex, setCurrentIndex] = React.useState(0);

  const nextGoal = () => {
    setCurrentIndex((prev) => (prev + 1) % goals.length);
  };

  const prevGoal = () => {
    setCurrentIndex((prev) => (prev - 1 + goals.length) % goals.length);
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateDaysLeft = (targetDate: string) => {
    const now = new Date();
    const target = new Date(targetDate);
    const diffTime = target.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  if (goals.length === 0) {
    return (
      <FintechCard variant="glassmorphic" className="p-6">
        <div className="text-center">
          <Target className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
          <h3 className="font-semibold mb-1">No Savings Goals</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Create your first savings goal to start tracking progress
          </p>
          <Button size="sm">Create Goal</Button>
        </div>
      </FintechCard>
    );
  }

  const currentGoal = goals[currentIndex];
  const progress = calculateProgress(currentGoal.currentAmount, currentGoal.targetAmount);

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold">Savings Goals</h3>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={prevGoal}
            disabled={goals.length <= 1}
            className="h-8 w-8"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={nextGoal}
            disabled={goals.length <= 1}
            className="h-8 w-8"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-start">
          <div>
            <h4 className="font-medium text-lg">{currentGoal.title}</h4>
            <p className="text-sm text-muted-foreground capitalize">
              {currentGoal.category} • {calculateDaysLeft(currentGoal.targetDate)} days left
            </p>
          </div>
          <div className="text-right">
            <p className="text-lg font-semibold">
              {formatCurrency(currentGoal.currentAmount)}
            </p>
            <p className="text-sm text-muted-foreground">
              of {formatCurrency(currentGoal.targetAmount)}
            </p>
          </div>
        </div>

        <div className="space-y-2">
          <Progress value={progress} className="h-2" />
          <p className="text-sm text-muted-foreground text-center">
            {progress.toFixed(1)}% completed
          </p>
        </div>

        <div className="flex gap-2">
          <Button size="sm" className="flex-1">
            Add Money
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            View Details
          </Button>
        </div>
      </div>

      {goals.length > 1 && (
        <div className="flex justify-center gap-1 mt-4">
          {goals.map((_, index) => (
            <div
              key={index}
              className={`h-2 w-2 rounded-full transition-colors ${
                index === currentIndex ? "bg-primary" : "bg-muted"
              }`}
            />
          ))}
        </div>
      )}
    </FintechCard>
  );
}