
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";

interface SavingsPlanFormProps {
  initialData?: {
    name: string;
    description: string;
    minimumAmount: string;
    durationDays: string;
    interestRate: string;
    active: boolean;
  };
  onSave: (data: any) => void;
  isLoading?: boolean;
}

export function SavingsPlanForm({ 
  initialData, 
  onSave, 
  isLoading = false 
}: SavingsPlanFormProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    description: initialData?.description || "",
    minimumAmount: initialData?.minimumAmount || "",
    durationDays: initialData?.durationDays || "",
    interestRate: initialData?.interestRate || "",
    active: initialData?.active ?? true
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, active: checked }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.minimumAmount || !formData.durationDays) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }
    
    const minimumAmount = parseFloat(formData.minimumAmount);
    const durationDays = parseInt(formData.durationDays);
    const interestRate = parseFloat(formData.interestRate);
    
    if (isNaN(minimumAmount) || minimumAmount <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid minimum amount",
        variant: "destructive"
      });
      return;
    }
    
    if (isNaN(durationDays) || durationDays <= 0) {
      toast({
        title: "Invalid duration",
        description: "Please enter a valid duration in days",
        variant: "destructive"
      });
      return;
    }
    
    if (isNaN(interestRate) || interestRate < 0) {
      toast({
        title: "Invalid interest rate",
        description: "Please enter a valid interest rate",
        variant: "destructive"
      });
      return;
    }
    
    onSave({
      ...formData,
      minimumAmount: minimumAmount.toString(),
      durationDays: durationDays.toString(),
      interestRate: interestRate.toString()
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Plan Name</Label>
        <Input
          id="name"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          placeholder="e.g., Daily Savings Plus"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Plan Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          placeholder="Describe the savings plan and its benefits"
          rows={3}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="minimumAmount">Minimum Amount (₦)</Label>
          <Input
            id="minimumAmount"
            name="minimumAmount"
            type="number"
            value={formData.minimumAmount}
            onChange={handleInputChange}
            placeholder="e.g., 1000"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="durationDays">Duration (Days)</Label>
          <Input
            id="durationDays"
            name="durationDays"
            type="number"
            value={formData.durationDays}
            onChange={handleInputChange}
            placeholder="e.g., 30"
            required
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="interestRate">Interest Rate (%)</Label>
        <Input
          id="interestRate"
          name="interestRate"
          type="number"
          step="0.01"
          value={formData.interestRate}
          onChange={handleInputChange}
          placeholder="e.g., 5.5"
        />
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="active"
          checked={formData.active}
          onCheckedChange={handleSwitchChange}
        />
        <Label htmlFor="active">Active</Label>
      </div>
      
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? "Saving..." : "Save Savings Plan"}
      </Button>
    </form>
  );
}
