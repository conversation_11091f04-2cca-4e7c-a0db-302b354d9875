const User = require('./user');
const SavingsPlan = require('./savingsPlan');
const Transaction = require('./transaction');
const Accounts = require('./accounts');
const Deposit = require('./deposit');
const GlobalSettings = require('./globalSettings');
const GroupSavingsPlan = require('./groupSavingsPlan');
const KYC = require('./kyc');
const Notification = require('./notification');
const Profit = require('./profit');
const RotationalGroupSavings = require('./rotationalGroupSavings');
const Withdraw = require('./withdraw');
const WithdrawAccounts = require('./withdrawAccounts');

module.exports = {
  User,
  SavingsPlan,
  Transaction,
  Accounts,
  Deposit,
  GlobalSettings,
  GroupSavingsPlan,
  KYC,
  Notification,
  Profit,
  RotationalGroupSavings,
  Withdraw,
  WithdrawAccounts,
};
