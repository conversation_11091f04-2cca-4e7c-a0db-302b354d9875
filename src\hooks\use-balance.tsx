
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';

export function useBalance() {
  const { user } = useAuth();
  const [balance, setBalance] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch balance from backend/localStorage
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        setIsLoading(true);
        // In a real app, this would be an API call
        // For now, we'll simulate this with localStorage
        if (user?.id) {
          const savedBalance = localStorage.getItem(`user_balance_${user.id}`);
          if (savedBalance) {
            setBalance(parseFloat(savedBalance));
          } else {
            // Default balance for demo
            setBalance(1250000);
            localStorage.setItem(`user_balance_${user.id}`, '1250000');
          }
        }
      } catch (error) {
        console.error('Error fetching balance:', error);
        toast.error('Failed to fetch your current balance');
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchBalance();
    }
  }, [user]);

  // Update balance function
  const updateBalance = async (amount: number, operation: 'add' | 'subtract') => {
    try {
      if (!user?.id) return false;
      
      let newBalance = balance;
      
      if (operation === 'add') {
        newBalance += amount;
      } else {
        if (balance < amount) {
          toast.error('Insufficient funds for this operation');
          return false;
        }
        newBalance -= amount;
      }
      
      // Update state and localStorage
      setBalance(newBalance);
      localStorage.setItem(`user_balance_${user.id}`, newBalance.toString());
      
      return true;
    } catch (error) {
      console.error('Error updating balance:', error);
      toast.error('Failed to update your balance');
      return false;
    }
  };

  // Format balance as Naira
  const formatBalance = (value: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return {
    balance,
    formattedBalance: formatBalance(balance),
    isLoading,
    updateBalance,
    formatBalance,
  };
}
