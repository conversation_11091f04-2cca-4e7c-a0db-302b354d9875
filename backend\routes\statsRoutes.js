const express = require('express');
const User = require('../models/user');
const Transaction = require('../models/transaction');
const Deposit = require('../models/deposit');
const Withdraw = require('../models/withdraw');
const KYC = require('../models/kyc');
const SavingsPlan = require('../models/savingsPlan');

const router = express.Router();

// GET /api/users/stats
router.get('/users/stats', async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const newUsersToday = await User.countDocuments({ createdAt: { $gte: today } });
    // Optionally, count verified users if you have a field for that
    res.json({ totalUsers, newUsersToday });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch user stats' });
  }
});

// GET /api/transactions/stats
router.get('/transactions/stats', async (req, res) => {
  try {
    const totalTransactions = await Transaction.countDocuments();
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const transactionsToday = await Transaction.countDocuments({ createdAt: { $gte: today } });
    const transactionsValue = await Transaction.aggregate([
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    // Count active savings plans (targetDate in the future and not completed)
    const now = new Date();
    const activeSavingsPlans = await SavingsPlan.countDocuments({ targetDate: { $gt: now } });
    res.json({
      totalTransactions,
      transactionsToday,
      transactionsValue: transactionsValue[0] ? transactionsValue[0].total : 0,
      activeSavingsPlans
    });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch transaction stats' });
  }
});

// GET /api/deposit/stats
router.get('/deposit/stats', async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const depositsToday = await Deposit.countDocuments({ createdAt: { $gte: today } });
    const depositsValue = await Deposit.aggregate([
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    res.json({
      depositsToday,
      depositsValue: depositsValue[0] ? depositsValue[0].total : 0
    });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch deposit stats' });
  }
});

// GET /api/withdraw/stats
router.get('/withdraw/stats', async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const withdrawalsToday = await Withdraw.countDocuments({ createdAt: { $gte: today } });
    const withdrawalsValue = await Withdraw.aggregate([
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    res.json({
      withdrawalsToday,
      withdrawalsValue: withdrawalsValue[0] ? withdrawalsValue[0].total : 0
    });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch withdrawal stats' });
  }
});

// GET /api/kyc/stats
router.get('/kyc/stats', async (req, res) => {
  try {
    const pendingKyc = await KYC.countDocuments({ kycStatus: { $in: ['Pending', 'Not Submitted'] } });
    res.json({ pendingKyc });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch KYC stats' });
  }
});

module.exports = router;
