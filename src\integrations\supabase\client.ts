// Placeholder Supabase client for build compatibility
export const supabase = {
  from: (table: string) => ({
    select: () => ({ data: [], error: null }),
    insert: () => ({ data: null, error: null }),
    update: () => ({ data: null, error: null }),
    delete: () => ({ data: null, error: null }),
  }),
  auth: {
    signUp: () => ({ data: null, error: null }),
    signIn: () => ({ data: null, error: null }),
    signOut: () => ({ error: null }),
  },
};