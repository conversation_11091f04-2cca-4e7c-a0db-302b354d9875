
import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { 
  Plus, 
  UserC<PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>,
  <PERSON>rash, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Define role types and permissions
const rolePermissions = [
  { id: "users_view", name: "View Users", description: "Can view user profiles" },
  { id: "users_edit", name: "Edit Users", description: "Can edit user information" },
  { id: "plans_view", name: "View Plans", description: "Can view savings plans" },
  { id: "plans_create", name: "Create Plans", description: "Can create new savings plans" },
  { id: "plans_edit", name: "Edit Plans", description: "Can modify existing savings plans" },
  { id: "plans_delete", name: "Delete Plans", description: "Can delete savings plans" },
  { id: "kyc_verify", name: "Verify KYC", description: "Can verify user KYC documents" },
  { id: "transactions_view", name: "View Transactions", description: "Can view all transactions" },
  { id: "reports_view", name: "View Reports", description: "Can view financial reports" },
  { id: "admin_manage", name: "Manage Admins", description: "Can manage other admin accounts" },
];

export default function AdminRolesManagement() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFormOpen, setRoleFormOpen] = useState(false);
  const [adminFormOpen, setAdminFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [viewRoleDetails, setViewRoleDetails] = useState(false);
  const [viewAdminDetails, setViewAdminDetails] = useState(false);
  
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const [selectedAdmin, setSelectedAdmin] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  
  // Form states
  const [roleFormData, setRoleFormData] = useState({
    name: "",
    description: "",
    permissions: [] as string[]
  });
  
  const [adminFormData, setAdminFormData] = useState({
    name: "",
    email: "",
    phone: "",
    roleId: "",
    isActive: true
  });
  
  // Mock data for roles
  const rolesData = [
    {
      id: "1",
      name: "Super Admin",
      description: "Has access to all features and functionalities",
      permissions: rolePermissions.map(p => p.id),
      createdAt: "2023-03-10",
      adminsCount: 2
    },
    {
      id: "2",
      name: "Plan Manager",
      description: "Can manage savings plans",
      permissions: ["plans_view", "plans_create", "plans_edit", "users_view"],
      createdAt: "2023-04-15",
      adminsCount: 3
    },
    {
      id: "3",
      name: "User Support",
      description: "Can view and help users with their accounts",
      permissions: ["users_view", "kyc_verify", "transactions_view"],
      createdAt: "2023-04-22",
      adminsCount: 5
    },
    {
      id: "4",
      name: "Verification Officer",
      description: "Handles KYC verifications only",
      permissions: ["users_view", "kyc_verify"],
      createdAt: "2023-05-30",
      adminsCount: 4
    }
  ];
  
  // Mock data for admins
  const adminsData = [
    {
      id: "1",
      name: "Oluwaseun Johnson",
      email: "<EMAIL>",
      phone: "+234 ************",
      roleId: "1",
      roleName: "Super Admin",
      isActive: true,
      lastLogin: "2023-06-20 10:30 AM"
    },
    {
      id: "2",
      name: "Adebayo Smith",
      email: "<EMAIL>",
      phone: "+234 ************",
      roleId: "2",
      roleName: "Plan Manager",
      isActive: true,
      lastLogin: "2023-06-19 02:15 PM"
    },
    {
      id: "3",
      name: "Chiamaka Okafor",
      email: "<EMAIL>",
      phone: "+234 ************",
      roleId: "3",
      roleName: "User Support",
      isActive: true,
      lastLogin: "2023-06-20 09:45 AM"
    },
    {
      id: "4",
      name: "Ibrahim Mohammed",
      email: "<EMAIL>",
      phone: "+234 ************",
      roleId: "4",
      roleName: "Verification Officer",
      isActive: false,
      lastLogin: "2023-06-15 11:20 AM"
    },
    {
      id: "5",
      name: "Funke Adeyemo",
      email: "<EMAIL>",
      phone: "+234 ************",
      roleId: "3",
      roleName: "User Support",
      isActive: true,
      lastLogin: "2023-06-18 03:40 PM"
    }
  ];
  
  const filteredRoles = rolesData.filter(role => 
    role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    role.description.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const filteredAdmins = adminsData.filter(admin => 
    admin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    admin.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    admin.roleName.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const handleCreateRole = () => {
    setRoleFormData({
      name: "",
      description: "",
      permissions: []
    });
    setIsEditMode(false);
    setRoleFormOpen(true);
  };
  
  const handleEditRole = (role: any) => {
    setSelectedRole(role);
    setRoleFormData({
      name: role.name,
      description: role.description,
      permissions: role.permissions
    });
    setIsEditMode(true);
    setRoleFormOpen(true);
  };
  
  const handleSubmitRole = () => {
    setIsLoading(true);
    
    // Validate form
    if (!roleFormData.name || !roleFormData.description || roleFormData.permissions.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields and select at least one permission.",
        variant: "destructive"
      });
      setIsLoading(false);
      return;
    }
    
    // Mock API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Success",
        description: isEditMode
          ? `Role "${roleFormData.name}" has been updated.`
          : `New role "${roleFormData.name}" has been created.`,
      });
      setRoleFormOpen(false);
    }, 1000);
  };
  
  const handleCreateAdmin = () => {
    setAdminFormData({
      name: "",
      email: "",
      phone: "",
      roleId: rolesData[0].id,
      isActive: true
    });
    setIsEditMode(false);
    setAdminFormOpen(true);
  };
  
  const handleEditAdmin = (admin: any) => {
    setSelectedAdmin(admin);
    setAdminFormData({
      name: admin.name,
      email: admin.email,
      phone: admin.phone,
      roleId: admin.roleId,
      isActive: admin.isActive
    });
    setIsEditMode(true);
    setAdminFormOpen(true);
  };
  
  const handleSubmitAdmin = () => {
    setIsLoading(true);
    
    // Validate form
    if (!adminFormData.name || !adminFormData.email || !adminFormData.roleId) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      setIsLoading(false);
      return;
    }
    
    // Mock API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Success",
        description: isEditMode
          ? `Admin "${adminFormData.name}" has been updated.`
          : `New admin "${adminFormData.name}" has been created.`,
      });
      setAdminFormOpen(false);
    }, 1000);
  };
  
  const handleDeleteItem = (item: any, isAdmin: boolean) => {
    if (isAdmin) {
      setSelectedAdmin(item);
    } else {
      setSelectedRole(item);
    }
    setDeleteDialogOpen(true);
  };
  
  const confirmDelete = () => {
    setIsLoading(true);
    
    // Mock API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Item Deleted",
        description: selectedAdmin 
          ? `Admin "${selectedAdmin.name}" has been deleted.`
          : `Role "${selectedRole.name}" has been deleted.`,
      });
      setDeleteDialogOpen(false);
    }, 1000);
  };

  const handleViewRole = (role: any) => {
    setSelectedRole(role);
    setViewRoleDetails(true);
  };

  const handleViewAdmin = (admin: any) => {
    setSelectedAdmin(admin);
    setViewAdminDetails(true);
  };
  
  return (
    <div className="container mx-auto max-w-7xl py-6 animate-fade-in">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Admin Roles Management</h1>
      </div>
      
      <Tabs defaultValue="roles" className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <TabsList className="bg-muted/50">
            <TabsTrigger value="roles">Roles</TabsTrigger>
            <TabsTrigger value="admins">Administrators</TabsTrigger>
          </TabsList>
          
          <div className="flex flex-col sm:flex-row gap-2 items-center">
            <div className="w-full sm:w-auto">
              <Input
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full sm:w-[250px]"
              />
            </div>
            <Button 
              onClick={TabsList ? handleCreateRole : handleCreateAdmin}
              className="bg-brand-blue text-white hover:bg-brand-blue/90 w-full sm:w-auto"
            >
              <Plus className="mr-2 h-4 w-4" /> 
              {TabsList ? "Add Role" : "Add Admin"}
            </Button>
          </div>
        </div>
        
        <TabsContent value="roles" className="mt-0">
          <Card className="shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Role Name</TableHead>
                      <TableHead className="hidden md:table-cell">Description</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Admins</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRoles.length > 0 ? (
                      filteredRoles.map((role) => (
                        <TableRow key={role.id} className="hover:bg-brand-blue/5 transition-colors">
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              <ShieldCheck className="h-5 w-5 mr-2 text-brand-blue" />
                              {role.name}
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">{role.description}</TableCell>
                          <TableCell>{role.permissions.length} permissions</TableCell>
                          <TableCell>
                            <Badge variant="outline" className="bg-brand-blue/10 border-brand-blue/30">
                              {role.adminsCount} admins
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleViewRole(role)}>
                                  <Eye className="mr-2 h-4 w-4" /> View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditRole(role)}>
                                  <Edit className="mr-2 h-4 w-4" /> Edit Role
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleDeleteItem(role, false)}
                                  disabled={role.name === "Super Admin"}
                                >
                                  <Trash className="mr-2 h-4 w-4" /> Delete Role
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6">
                          No roles found matching your search.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="bg-muted/20 py-3">
              <div className="flex justify-between items-center w-full">
                <p className="text-sm text-muted-foreground">Showing {filteredRoles.length} of {rolesData.length} roles</p>
                <Button 
                  onClick={handleCreateRole}
                  variant="outline" 
                  size="sm" 
                  className="text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white"
                >
                  <Plus className="mr-2 h-4 w-4" /> Add New Role
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="admins" className="mt-0">
          <Card className="shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="hidden md:table-cell">Last Login</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAdmins.length > 0 ? (
                      filteredAdmins.map((admin) => (
                        <TableRow key={admin.id} className="hover:bg-brand-blue/5 transition-colors">
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              <UserCog className="h-5 w-5 mr-2 text-brand-blue" />
                              {admin.name}
                            </div>
                          </TableCell>
                          <TableCell>{admin.email}</TableCell>
                          <TableCell>
                            <Badge variant="outline" className="bg-brand-blue/10 border-brand-blue/30">
                              {admin.roleName}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={admin.isActive ? "default" : "secondary"} className={admin.isActive ? "bg-green-500" : "bg-gray-500"}>
                              {admin.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">{admin.lastLogin}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleViewAdmin(admin)}>
                                  <Eye className="mr-2 h-4 w-4" /> View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditAdmin(admin)}>
                                  <Edit className="mr-2 h-4 w-4" /> Edit Admin
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Key className="mr-2 h-4 w-4" /> Reset Password
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleDeleteItem(admin, true)}
                                  disabled={admin.roleName === "Super Admin"}
                                >
                                  <Trash className="mr-2 h-4 w-4" /> Delete Admin
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-6">
                          No administrators found matching your search.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="bg-muted/20 py-3">
              <div className="flex justify-between items-center w-full">
                <p className="text-sm text-muted-foreground">Showing {filteredAdmins.length} of {adminsData.length} administrators</p>
                <Button 
                  onClick={handleCreateAdmin}
                  variant="outline" 
                  size="sm" 
                  className="text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white"
                >
                  <Plus className="mr-2 h-4 w-4" /> Add New Admin
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Role Form Dialog */}
      <Dialog open={roleFormOpen} onOpenChange={setRoleFormOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>{isEditMode ? "Edit Role" : "Create New Role"}</DialogTitle>
            <DialogDescription>
              {isEditMode 
                ? "Update the role details and permissions." 
                : "Define a new administrative role with specific permissions."}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-name" className="text-right">Role Name</Label>
              <Input
                id="role-name"
                placeholder="e.g. Customer Support"
                className="col-span-3"
                value={roleFormData.name}
                onChange={(e) => setRoleFormData({...roleFormData, name: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-description" className="text-right">Description</Label>
              <Input
                id="role-description"
                placeholder="Briefly describe this role's purpose"
                className="col-span-3"
                value={roleFormData.description}
                onChange={(e) => setRoleFormData({...roleFormData, description: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <Label className="text-right pt-2">Permissions</Label>
              <div className="col-span-3">
                <Card className="border-dashed">
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {rolePermissions.map((permission) => (
                        <div key={permission.id} className="flex items-start space-x-2">
                          <Checkbox 
                            id={permission.id}
                            checked={roleFormData.permissions.includes(permission.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setRoleFormData({
                                  ...roleFormData, 
                                  permissions: [...roleFormData.permissions, permission.id]
                                });
                              } else {
                                setRoleFormData({
                                  ...roleFormData,
                                  permissions: roleFormData.permissions.filter(p => p !== permission.id)
                                });
                              }
                            }}
                          />
                          <div className="leading-none">
                            <Label 
                              htmlFor={permission.id}
                              className="text-sm cursor-pointer"
                            >
                              {permission.name}
                            </Label>
                            <p className="text-xs text-muted-foreground pt-1">
                              {permission.description}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setRoleFormOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmitRole} 
              disabled={isLoading} 
              className="bg-brand-blue hover:bg-brand-blue/90 text-white"
            >
              {isLoading ? "Saving..." : isEditMode ? "Update Role" : "Create Role"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Admin Form Dialog */}
      <Dialog open={adminFormOpen} onOpenChange={setAdminFormOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{isEditMode ? "Edit Administrator" : "Add New Administrator"}</DialogTitle>
            <DialogDescription>
              {isEditMode 
                ? "Update the administrator details." 
                : "Create a new administrator account with specific role."}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="admin-name" className="text-right">Full Name</Label>
              <Input
                id="admin-name"
                placeholder="Administrator name"
                className="col-span-3"
                value={adminFormData.name}
                onChange={(e) => setAdminFormData({...adminFormData, name: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="admin-email" className="text-right">Email</Label>
              <Input
                id="admin-email"
                placeholder="<EMAIL>"
                type="email"
                className="col-span-3"
                value={adminFormData.email}
                onChange={(e) => setAdminFormData({...adminFormData, email: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="admin-phone" className="text-right">Phone</Label>
              <Input
                id="admin-phone"
                placeholder="+234 ************"
                className="col-span-3"
                value={adminFormData.phone}
                onChange={(e) => setAdminFormData({...adminFormData, phone: e.target.value})}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="admin-role" className="text-right">Role</Label>
              <select
                id="admin-role"
                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={adminFormData.roleId}
                onChange={(e) => setAdminFormData({...adminFormData, roleId: e.target.value})}
              >
                {rolesData.map(role => (
                  <option key={role.id} value={role.id}>{role.name}</option>
                ))}
              </select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="admin-status" className="text-right">Status</Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Checkbox 
                  id="admin-status" 
                  checked={adminFormData.isActive}
                  onCheckedChange={(checked) => setAdminFormData({
                    ...adminFormData, 
                    isActive: checked as boolean
                  })}
                />
                <Label htmlFor="admin-status" className="cursor-pointer">Active</Label>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setAdminFormOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmitAdmin} 
              disabled={isLoading} 
              className="bg-brand-blue hover:bg-brand-blue/90 text-white"
            >
              {isLoading ? "Saving..." : isEditMode ? "Update Admin" : "Create Admin"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this {selectedAdmin ? "administrator" : "role"}?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDelete} 
              disabled={isLoading}
            >
              {isLoading ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* View Role Details Dialog */}
      <Dialog open={viewRoleDetails} onOpenChange={setViewRoleDetails}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>Role Details</DialogTitle>
            <DialogDescription>
              Complete information about this administrative role.
            </DialogDescription>
          </DialogHeader>
          
          {selectedRole && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <ShieldCheck className="h-6 w-6 text-brand-blue" />
                <h3 className="text-xl font-semibold">{selectedRole.name}</h3>
              </div>
              
              <p className="text-sm text-muted-foreground">{selectedRole.description}</p>
              
              <div>
                <h4 className="font-medium mb-2">Permissions</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {rolePermissions
                    .filter(p => selectedRole.permissions.includes(p.id))
                    .map(permission => (
                      <div key={permission.id} className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span className="text-sm">{permission.name}</span>
                      </div>
                    ))
                  }
                </div>
              </div>
              
              <div className="pt-2">
                <div className="flex justify-between items-center border-t pt-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Created on</p>
                    <p className="font-medium">{selectedRole.createdAt}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Administrators</p>
                    <p className="font-medium">{selectedRole.adminsCount} assigned</p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setViewRoleDetails(false);
                    handleEditRole(selectedRole);
                  }}
                >
                  <Edit className="h-4 w-4 mr-1" /> Edit Role
                </Button>
                <Button 
                  className="bg-brand-blue text-white hover:bg-brand-blue/90" 
                  size="sm"
                >
                  <Users className="h-4 w-4 mr-1" /> View Assigned Admins
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* View Admin Details Dialog */}
      <Dialog open={viewAdminDetails} onOpenChange={setViewAdminDetails}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>Administrator Details</DialogTitle>
            <DialogDescription>
              Complete information about this administrator account.
            </DialogDescription>
          </DialogHeader>
          
          {selectedAdmin && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <UserCog className="h-6 w-6 text-brand-blue" />
                <h3 className="text-xl font-semibold">{selectedAdmin.name}</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{selectedAdmin.email}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Phone</p>
                  <p className="font-medium">{selectedAdmin.phone}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Role</p>
                  <Badge className="mt-1 bg-brand-blue text-white">{selectedAdmin.roleName}</Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <Badge 
                    variant={selectedAdmin.isActive ? "default" : "secondary"}
                    className={`mt-1 ${selectedAdmin.isActive ? "bg-green-500" : "bg-gray-500"}`}
                  >
                    {selectedAdmin.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
              
              <div className="pt-2">
                <div className="border-t pt-4">
                  <p className="text-sm text-muted-foreground">Last Login</p>
                  <p className="font-medium">{selectedAdmin.lastLogin}</p>
                </div>
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setViewAdminDetails(false);
                    handleEditAdmin(selectedAdmin);
                  }}
                >
                  <Edit className="h-4 w-4 mr-1" /> Edit Account
                </Button>
                <Button 
                  className="bg-brand-yellow text-foreground hover:bg-brand-yellow/90 hover:shadow-yellow" 
                  size="sm"
                >
                  <Key className="h-4 w-4 mr-1" /> Reset Password
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
