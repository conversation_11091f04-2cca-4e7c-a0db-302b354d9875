const mongoose = require('mongoose');

const ProfitSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  savingsPlanId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SavingsPlan',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  interestRate: {
    type: Number,
    required: true,
  },
  calculationDate: {
    type: Date,
    default: Date.now,
  },
  description: {
    type: String,
    default: 'Interest accrued'
  },
  status: {
    type: String,
    enum: ['pending', 'applied', 'cancelled'],
    default: 'pending',
  },
});

module.exports = mongoose.model('Profit', ProfitSchema);
