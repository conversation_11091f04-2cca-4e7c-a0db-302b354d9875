import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { useToast } from '@/hooks/use-toast';
import { Wallet, Plus, Minus, TrendingUp } from 'lucide-react';

const FlexSavingsCard = () => {
  const [balance] = useState(45000);
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const dailyInterest = 0.0411; // 15% annually
  const todaysEarning = balance * (dailyInterest / 100);

  const handleDeposit = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast({
        title: "Deposit Successful!",
        description: `₦${parseFloat(amount).toLocaleString()} added to your Flex Savings.`,
      });
      setAmount('');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process deposit.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleWithdraw = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast({
        title: "Withdrawal Successful!",
        description: `₦${parseFloat(amount).toLocaleString()} withdrawn from your Flex Savings.`,
      });
      setAmount('');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process withdrawal.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <Wallet className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">Flex Savings</h2>
          <p className="text-sm text-muted-foreground">Save with daily interest</p>
        </div>
      </div>

      <div className="space-y-4 mb-6">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Current Balance</p>
          <p className="text-3xl font-bold">₦{balance.toLocaleString()}</p>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">Today's Earning</span>
          </div>
          <p className="text-lg font-semibold text-green-600">
            +₦{todaysEarning.toFixed(2)}
          </p>
          <p className="text-xs text-green-600/80">15% annual rate</p>
        </div>
      </div>

      <div className="space-y-4">
        <FloatingLabelInput
          id="amount"
          type="number"
          label="Amount (₦)"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="1,000"
        />

        <div className="grid grid-cols-2 gap-3">
          <Button 
            variant="outline" 
            onClick={handleDeposit}
            disabled={loading || !amount}
            className="border-primary text-primary hover:bg-primary hover:text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            Deposit
          </Button>
          <Button 
            variant="outline" 
            onClick={handleWithdraw}
            disabled={loading || !amount || parseFloat(amount) > balance}
            className="border-gray-300 text-gray-600 hover:bg-gray-100"
          >
            <Minus className="mr-2 h-4 w-4" />
            Withdraw
          </Button>
        </div>
      </div>
    </FintechCard>
  );
};

export default FlexSavingsCard;