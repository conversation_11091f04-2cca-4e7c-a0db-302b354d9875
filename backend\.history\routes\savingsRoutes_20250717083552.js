const express = require('express');
const SavingsPlan = require('../models/savingsPlan');
const Transaction = require('../models/transaction');
const { User } = require('../models');
const mongoose = require('mongoose');
const { authenticateToken } = require('../middleware/authMiddleware');
const { getInterestRate } = require('../utils/interestRateUtil');

const router = express.Router();


router.get('/achievements', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    // Support both ObjectId and string for userId
    const userIdObj = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;
    const userIdQuery = userIdObj ? { $in: [userId, userIdObj] } : userId;
    // Calculate rank based on total saved
    const transactions = await Transaction.find({ userId: userIdQuery, type: 'deposit', status: 'completed' });
    const totalSaved = transactions.reduce((sum, t) => sum + t.amount, 0);
    let rank = 'Bronze';
    if (totalSaved >= 1000000) rank = 'Gold';
    else if (totalSaved >= 500000) rank = 'Silver';
    else if (totalSaved >= 100000) rank = 'Bronze';
    // Example badges
    const badges = [];
    if (totalSaved > 0) badges.push({ name: 'First Saver', icon: 'trophy' });
    if (transactions.length >= 10) badges.push({ name: 'Consistent', icon: 'calendar' });
    if (totalSaved >= 1000000) badges.push({ name: 'Millionaire', icon: 'medal' });
    // Example: days since first deposit
    let savingsDuration = 0;
    if (transactions.length > 0) {
      const first = transactions.reduce((min, t) => t.date < min.date ? t : min, transactions[0]);
      const now = new Date();
      savingsDuration = Math.floor((now - first.date) / (1000 * 60 * 60 * 24));
    }
    // Consistency: % of days with a deposit in last 30 days
    const last30 = transactions.filter(t => {
      const d = new Date(t.date);
      return d >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    });
    const daysWithDeposit = new Set(last30.map(t => new Date(t.date).toDateString())).size;
    const consistencyScore = Math.round((daysWithDeposit / 30) * 100);
    console.log('[ACHIEVEMENTS]', {
      userId,
      totalSaved,
      rank,
      badges,
      savingsDuration,
      consistencyScore,
      depositCount: transactions.length
    });
    res.json({ rank, badges, savingsDuration, consistencyScore });
  } catch (error) {
    console.error('Error fetching achievements:', error);
    res.status(500).json({ error: 'Failed to fetch achievements', details: error.message });
  }
});

// GET /api/savings/history - User savings history for chart
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    // Support both ObjectId and string for userId
    const userIdObj = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;
    const userIdQuery = userIdObj ? { $in: [userId, userIdObj] } : userId;
    // All relevant transactions for this user
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth() - 11, 1); // 12 months ago
    const transactions = await Transaction.find({
      userId: userIdQuery,
      type: 'deposit',
      status: 'completed',
      date: { $gte: start }
    });
    // Group by month
    const monthlyMap = {};
    for (const tx of transactions) {
      const d = new Date(tx.date);
      const key = `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
      if (!monthlyMap[key]) monthlyMap[key] = 0;
      monthlyMap[key] += tx.amount || 0;
    }
    // Format for chart: [{ month: '2025-01', total: 10000 }, ...]
    const chartData = [];
    for (let i = 0; i < 12; i++) {
      const d = new Date(now.getFullYear(), now.getMonth() - 11 + i, 1);
      const key = `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
      chartData.push({
        month: key,
        total: monthlyMap[key] || 0
      });
    }
    res.json(chartData);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch savings history', details: error.message });
  }
});

// GET /api/savings/timeline - User activity timeline (recent savings/withdrawals)
router.get('/timeline', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    // Support both ObjectId and string for userId
    const userIdObj = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;
    const userIdQuery = userIdObj ? { $in: [userId, userIdObj] } : userId;
    // Get last 20 transactions (deposit/withdrawal)
    const txs = await Transaction.find({ userId: userIdQuery, status: 'completed' })
      .sort({ date: -1 })
      .limit(20);
    console.log('[TIMELINE][DEBUG] userId type:', typeof userId, 'userId value:', userId);
    if (txs.length === 0) {
      console.log('[TIMELINE][DEBUG] No transactions found for user:', userId);
    } else {
      console.log('[TIMELINE][DEBUG] First 3 transactions:', txs.slice(0, 3));
    }
    // Format: [{ type, amount, date, description }]
    const timeline = txs.map(t => ({
      type: t.type,
      amount: t.amount,
      date: t.date,
      description: t.description
    }));
    console.log('[TIMELINE]', { userId, count: timeline.length, timeline });
    res.json(timeline);
  } catch (error) {
    console.error('Error fetching timeline:', error);
    res.status(500).json({ error: 'Failed to fetch timeline', details: error.message });
  }
});

// Create a new savings plan
router.post('/plan', authenticateToken, async (req, res) => {
  try {
    const { title, depositFrequency, depositAmount, targetDate, targetAmount } = req.body;
    const userId = req.user && req.user.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    if (!title || !depositFrequency || !depositAmount || !targetDate || !targetAmount) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    if (typeof depositAmount !== 'number' || depositAmount <= 0) {
      return res.status(400).json({ error: 'Deposit amount must be a positive number.' });
    }
    if (typeof targetAmount !== 'number' || targetAmount <= 0) {
      return res.status(400).json({ error: 'Target amount must be a positive number.' });
    }
    // Optionally: validate date format
    if (isNaN(Date.parse(targetDate))) {
      return res.status(400).json({ error: 'Target date is invalid.' });
    }
    // Enforce sufficient balance before creating plan
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found.' });
    }
    if (typeof user.balance !== 'number' || user.balance < depositAmount) {
      return res.status(400).json({
        error: 'Insufficient balance to create savings plan.',
        requiredAmount: depositAmount,
        currentBalance: user.balance || 0
      });
    }
    const newPlan = new SavingsPlan({
      title,
      depositFrequency,
      depositAmount,
      targetDate,
      targetAmount,
      userId,
    });
    const savedPlan = await newPlan.save();
    res.status(201).json(savedPlan);
  } catch (error) {
    console.error('Error creating savings plan:', error); // Log the detailed error
    res.status(500).json({ error: 'Failed to create savings plan', details: error.message });
  }
});

// Get all savings plans for the logged-in user, plus summary
router.get('/my', authenticateToken, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    console.log('[SavingsRoute] /my called for user:', userId);
    if (!userId) {
      console.error('[SavingsRoute] No userId found in request.');
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }

    // Fetch only individual plans
    let plans = [];
    try {
      plans = await SavingsPlan.find({ userId }).sort({ createdAt: -1 });
      console.log('[SavingsRoute] Plans found:', plans.length, plans);
    } catch (err) {
      console.error('[SavingsRoute] Error fetching plans:', err);
      return res.status(500).json({ error: 'Failed to fetch savings plans', details: err.message });
    }

    // Calculate summary statistics
    const totalPlans = plans.length;
    const now = new Date();
    const activePlans = plans.filter(plan => {
      try {
        return new Date(plan.targetDate) > now;
      } catch (e) {
        console.error('[SavingsRoute] Error parsing plan.targetDate:', plan.targetDate, e);
        return false;
      }
    }).length;
    const completedPlans = totalPlans - activePlans;

    // Fetch transactions
    let transactions = [];
    try {
      transactions = await Transaction.find({ 
        userId, 
        type: 'deposit',
        status: 'completed'
      });
      console.log('[SavingsRoute] Transactions found:', transactions.length, transactions);
    } catch (err) {
      console.error('[SavingsRoute] Error fetching transactions:', err);
      return res.status(500).json({ error: 'Failed to fetch transactions', details: err.message });
    }

    const totalSaved = transactions.reduce((sum, transaction) => sum + (transaction.amount || 0), 0);
    const totalTargetAmount = plans.reduce((sum, plan) => sum + (plan.targetAmount || 0), 0);
    const savingsProgress = totalTargetAmount > 0 ? (totalSaved / totalTargetAmount) * 100 : 0;

    console.log('[SavingsRoute] Summary:', {
      totalPlans,
      activePlans,
      completedPlans,
      totalSaved,
      totalTargetAmount,
      savingsProgress
    });



    // Add currentAmount, interestAccrued, and interestRate to each plan (individual only)
    for (let i = 0; i < plans.length; i++) {
      const plan = plans[i];
      const planObj = plan.toObject();
      planObj.currentAmount = planObj.savedAmount || 0;
      planObj.progress = planObj.targetAmount > 0 ? Math.round((planObj.currentAmount / planObj.targetAmount) * 100) : 0;
      // Calculate total interest accrued for this plan
      const interestTxs = await Transaction.find({ userId, type: 'interest', description: { $regex: plan._id.toString() } });
      planObj.interestAccrued = interestTxs.reduce((sum, tx) => sum + (tx.amount || 0), 0);
      // Add interest rate for this plan (individual)
      planObj.interestRate = await getInterestRate('individual');
      plans[i] = planObj;
    }

    // Calculate total interest earned (all interest transactions for user)
    let totalInterestEarned = 0;
    try {
      const allInterest = await Transaction.find({ userId, type: 'interest' });
      totalInterestEarned = allInterest.reduce((sum, t) => sum + (t.amount || 0), 0);
    } catch (err) {
      console.error('[SavingsRoute] Error fetching interest transactions:', err);
    }

    // Log the full response for debugging
    console.log('[SavingsRoute] /my response:', {
      plans,
      summary: {
        totalPlans,
        activePlans,
        completedPlans,
        totalSaved,
        totalTargetAmount,
        savingsProgress,
        totalInterestEarned
      }
    });
    res.status(200).json({
      plans,
      summary: {
        totalPlans,
        activePlans,
        completedPlans,
        totalSaved,
        totalTargetAmount,
        savingsProgress,
        totalInterestEarned
      }
    });
  } catch (error) {
    console.error('[SavingsRoute] Error in /my route:', error);
    res.status(500).json({ error: 'Failed to fetch savings plans', details: error.message });
  }
});

// Get savings plans with pagination and filtering
router.get('/plans', async (req, res) => {
  try {
    const { status, limit = 10, page = 1 } = req.query;
    // Try to get userId from auth if available, else allow public fetch (for all plans)
    let userId = undefined;
    if (req.user && req.user.id) {
      userId = req.user.id;
    }
    // Build query
    const query = {};
    if (userId) query.userId = userId;
    if (status) query.status = status;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Fetch plans with pagination
    const plans = await SavingsPlan.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalCount = await SavingsPlan.countDocuments(query);

    res.status(200).json({
      plans,
      pagination: {
        total: totalCount,
        page: parseInt(page),
        pages: Math.ceil(totalCount / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error('Error fetching savings plans:', error);
    res.status(500).json({ error: 'Failed to fetch savings plans', details: error.message });
  }
});

// Get savings summary for the logged-in user
router.get('/summary', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    // Support both ObjectId and string for userId
    const userIdObj = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;
    const userIdQuery = userIdObj ? { $in: [userId, userIdObj] } : userId;

    // Individual plans
    const plans = await SavingsPlan.find({ userId });
    // Group plans (where user is a member)
    const GroupSavingsPlan = require('../models/groupSavingsPlan');
    const groupPlans = await GroupSavingsPlan.find({ members: userId });
    // Rotational group plans (where user is a member)
    const RotationalGroupSavings = require('../models/rotationalGroupSavings');
    const rotationalPlans = await RotationalGroupSavings.find({ 'members.userId': userId });

    // Calculate total target amount (only user's share)
    let totalTargetAmount = 0;
    // Individual
    totalTargetAmount += plans.reduce((sum, plan) => sum + (plan.targetAmount || 0), 0);
    // Group: only user's share (assume equal split, only if user is a member)
    for (const plan of groupPlans) {
      if (Array.isArray(plan.members) && plan.members.map(id => String(id)).includes(String(userId))) {
        const membersCount = plan.members.length || 1;
        totalTargetAmount += (plan.targetAmount || 0) / membersCount;
      }
    }
    // Rotational: only user's share (amountPerInterval per cycle, only if user is a member)
    for (const plan of rotationalPlans) {
      if (Array.isArray(plan.members) && plan.members.some(m => String(m.userId) === String(userId))) {
        totalTargetAmount += plan.amountPerInterval || 0;
      }
    }

    // Improved logic: Only count deposits where description contains a number (amount) matching a plan's depositAmount or targetAmount, or description contains the word 'plan' or 'savings'.
    // This is a fallback for inconsistent descriptions.
    const allDeposits = await Transaction.find({
      userId: userIdQuery,
      type: 'deposit'
    });
    // Build a set of all possible relevant numbers (depositAmount, targetAmount) for all plans
    const relevantAmounts = [
      ...plans.map(p => p.depositAmount),
      ...plans.map(p => p.targetAmount),
      ...groupPlans.map(p => p.depositAmount),
      ...groupPlans.map(p => p.targetAmount),
      ...rotationalPlans.map(p => p.amountPerInterval)
    ].filter(Boolean).map(a => Number(a));
    let totalSaved = allDeposits.reduce((sum, t) => {
      const desc = t.description ? t.description.toLowerCase() : '';
      // If description contains a relevant amount, or contains 'plan' or 'savings', count it
      const matchesAmount = relevantAmounts.some(a => desc.includes(a.toString()));
      const matchesKeyword = desc.includes('plan') || desc.includes('savings');
      if (matchesAmount || matchesKeyword) {
        return sum + (t.amount || 0);
      }
      return sum;
    }, 0);

    // Calculate total interest earned (all interest transactions for user)
    const allInterest = await Transaction.find({
      userId: userIdQuery,
      type: 'interest'
    });
    let totalInterestEarned = allInterest.reduce((sum, t) => sum + (t.amount || 0), 0);

    // Count all plans
    const totalPlans = plans.length + groupPlans.length + rotationalPlans.length;
    const activePlans = [
      ...plans,
      ...groupPlans,
      ...rotationalPlans
    ].filter(plan => new Date(plan.targetDate || plan.nextPayoutDate) > new Date()).length;
    const completedPlans = totalPlans - activePlans;

    const summary = {
      totalPlans,
      activePlans,
      completedPlans,
      totalSaved,
      totalInterestEarned,
      totalTargetAmount,
      savingsProgress: totalTargetAmount > 0 ? (totalSaved / totalTargetAmount) * 100 : 0,
    };
    console.log('[SUMMARY]', { userId, ...summary });
    res.status(200).json(summary);
  } catch (error) {
    console.error('Error fetching savings summary:', error);
    res.status(500).json({ error: 'Failed to fetch savings summary', details: error.message });
  }
});

// Update a savings plan
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updatedPlan = await SavingsPlan.findByIdAndUpdate(id, req.body, { new: true });
    res.status(200).json(updatedPlan);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update savings plan', details: error.message });
  }
});

// Delete a savings plan
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await SavingsPlan.findByIdAndDelete(id);
    res.status(200).json({ message: 'Savings plan deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete savings plan', details: error.message });
  }
});

module.exports = router;

// GET /api/savings/group/active - Get all active group savings plans for the logged-in user
// GET /api/savings/group/active - Get all active group savings plans for the logged-in user
// GET /api/savings/rotational/active - Get all active rotational group savings plans for the logged-in user
router.get('/group/active', authenticateToken, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const GroupSavingsPlan = require('../models/groupSavingsPlan');
    const now = new Date();
    const groupPlans = await GroupSavingsPlan.find({
      members: userId,
      targetDate: { $gt: now }
    }).sort({ createdAt: -1 });
    console.log('[GROUP ACTIVE][DEBUG] userId:', userId, 'now:', now, 'groupPlans:', groupPlans);
    res.status(200).json({ activeGroups: groupPlans, count: groupPlans.length });
  } catch (error) {
    console.error('Error fetching active group savings:', error);
    res.status(500).json({ error: 'Failed to fetch active group savings', details: error.message });
  }
});

router.get('/rotational/active', authenticateToken, async (req, res) => {
  try {
    const userId = req.user && req.user.id;
    if (!userId) {
      return res.status(401).json({ error: 'User authentication failed. Please log in again.' });
    }
    const RotationalGroupSavings = require('../models/rotationalGroupSavings');
    const now = new Date();
    // Only plans where user is a member and nextPayoutDate or targetDate is in the future
    const rotationalPlans = await RotationalGroupSavings.find({
      'members.userId': userId,
      $or: [
        { targetDate: { $gt: now } },
        { nextPayoutDate: { $gt: now } }
      ]
    }).sort({ createdAt: -1 });
    res.status(200).json({ activeRotationalGroups: rotationalPlans, count: rotationalPlans.length });
  } catch (error) {
    console.error('Error fetching active rotational group savings:', error);
    res.status(500).json({ error: 'Failed to fetch active rotational group savings', details: error.message });
  }
});