import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CreditCard, Shield, Zap } from "lucide-react";
import { toast } from "sonner";

interface PaystackPaymentProps {
  onSuccess: (amount: number) => void;
  onError: (error: string) => void;
}

export const PaystackPayment = ({ onSuccess, onError }: PaystackPaymentProps) => {
  const [amount, setAmount] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handlePayment = async () => {
    if (!amount || parseFloat(amount) < 100) {
      toast.error("Minimum deposit amount is ₦100");
      return;
    }

    setIsLoading(true);
    
    try {
      // TODO: Integrate with Paystack API
      // This will require Supabase edge functions for secure API key handling
      
      // Mock successful payment for now
      setTimeout(() => {
        onSuccess(parseFloat(amount));
        toast.success("Payment successful! Your account will be credited shortly.");
        setIsLoading(false);
        setAmount("");
      }, 2000);
      
    } catch (error: any) {
      setIsLoading(false);
      onError(error.message || "Payment failed");
      toast.error("Payment failed. Please try again.");
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <CreditCard className="h-5 w-5 text-primary" />
          <CardTitle>Deposit Funds</CardTitle>
        </div>
        <CardDescription>
          Secure automatic payment via Paystack
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount (₦)</Label>
          <Input
            id="amount"
            type="number"
            placeholder="Enter amount"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            min="100"
          />
          <p className="text-xs text-muted-foreground">Minimum: ₦100</p>
        </div>

        <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <Shield className="h-4 w-4 text-green-600" />
          <span className="text-sm text-green-700 dark:text-green-400">
            Secured by Paystack SSL encryption
          </span>
        </div>

        <Button 
          onClick={handlePayment} 
          disabled={isLoading || !amount}
          className="w-full gap-2"
        >
          {isLoading ? (
            <>
              <span className="animate-spin">⟳</span>
              Processing...
            </>
          ) : (
            <>
              <Zap className="h-4 w-4" />
              Pay ₦{amount || "0"}
            </>
          )}
        </Button>

        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            Your payment is secured and encrypted
          </p>
        </div>
      </CardContent>
    </Card>
  );
};