
import { useState } from "react";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, Search, Shield, X } from "lucide-react";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { toast } from "sonner";

// Sample data for demonstration
const MOCK_USERS = [
  { id: "1", name: "<PERSON>", phone: "+2348012345678", email: "<EMAIL>", verified: false, lastOtpSent: "2023-04-12T14:30:00" },
  { id: "2", name: "<PERSON>", phone: "+2348023456789", email: "<EMAIL>", verified: true, lastOtpSent: "2023-04-10T09:15:00" },
  { id: "3", name: "<PERSON>", phone: "+2348034567890", email: "mi<PERSON><PERSON>@example.com", verified: false, lastOtpSent: "2023-04-11T16:45:00" },
  { id: "4", name: "<PERSON> <PERSON>", phone: "+2348045678901", email: "<EMAIL>", verified: true, lastOtpSent: "2023-04-09T11:20:00" },
  { id: "5", name: "David Brown", phone: "+2348056789012", email: "<EMAIL>", verified: false, lastOtpSent: "2023-04-12T08:30:00" },
];

const OtpVerificationManagement = () => {
  const [users, setUsers] = useState(MOCK_USERS);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState<typeof MOCK_USERS[0] | null>(null);
  const [verifyDialogOpen, setVerifyDialogOpen] = useState(false);
  const [resendDialogOpen, setResendDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.phone.includes(searchTerm) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleVerifyUser = (user: typeof MOCK_USERS[0]) => {
    setSelectedUser(user);
    setVerifyDialogOpen(true);
  };

  const handleResendOtp = (user: typeof MOCK_USERS[0]) => {
    setSelectedUser(user);
    setResendDialogOpen(true);
  };

  const confirmVerify = () => {
    if (!selectedUser) return;
    
    setIsProcessing(true);
    
    // Simulate API call
    setTimeout(() => {
      setUsers(users.map(user => 
        user.id === selectedUser.id ? { ...user, verified: true } : user
      ));
      
      setIsProcessing(false);
      setVerifyDialogOpen(false);
      
      toast.success(`${selectedUser.name}'s account has been verified`);
    }, 1000);
  };

  const confirmResendOtp = () => {
    if (!selectedUser) return;
    
    setIsProcessing(true);
    
    // Simulate API call
    setTimeout(() => {
      setUsers(users.map(user => 
        user.id === selectedUser.id ? { 
          ...user, 
          lastOtpSent: new Date().toISOString() 
        } : user
      ));
      
      setIsProcessing(false);
      setResendDialogOpen(false);
      
      toast.success(`OTP has been sent to ${selectedUser.name}`);
    }, 1000);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">OTP Verification Management</h1>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Last OTP Sent</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.phone}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    {user.verified ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <Check className="mr-1 h-3 w-3" /> Verified
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(user.lastOtpSent).toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {!user.verified && (
                        <Button
                          onClick={() => handleVerifyUser(user)}
                          size="sm"
                          variant="brand"
                          className="h-8"
                        >
                          <Shield className="mr-1 h-3 w-3" />
                          Verify
                        </Button>
                      )}
                      <Button
                        onClick={() => handleResendOtp(user)}
                        size="sm"
                        variant={user.verified ? "outline" : "yellow"}
                        className="h-8"
                      >
                        Resend OTP
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  No users found matching your search
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={verifyDialogOpen} onOpenChange={setVerifyDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Verify User Account</DialogTitle>
            <DialogDescription>
              Are you sure you want to manually verify {selectedUser?.name}'s account?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm font-medium">User Details:</p>
            <p className="text-sm text-muted-foreground">Phone: {selectedUser?.phone}</p>
            <p className="text-sm text-muted-foreground">Email: {selectedUser?.email}</p>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setVerifyDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button 
              onClick={confirmVerify}
              disabled={isProcessing}
              className="bg-brand-blue hover:bg-brand-blue/90"
            >
              {isProcessing ? "Processing..." : "Confirm Verification"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={resendDialogOpen} onOpenChange={setResendDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Resend OTP</DialogTitle>
            <DialogDescription>
              Send a new OTP verification code to {selectedUser?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm font-medium">Send OTP to:</p>
            <div className="flex gap-2 mt-2">
              <Button 
                variant="outline" 
                className="flex-1"
                disabled={isProcessing}
              >
                SMS ({selectedUser?.phone})
              </Button>
              <Button 
                variant="outline" 
                className="flex-1"
                disabled={isProcessing}
              >
                Email ({selectedUser?.email})
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setResendDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button 
              onClick={confirmResendOtp}
              disabled={isProcessing}
              className="bg-brand-blue hover:bg-brand-blue/90"
            >
              {isProcessing ? "Sending..." : "Send OTP"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OtpVerificationManagement;
