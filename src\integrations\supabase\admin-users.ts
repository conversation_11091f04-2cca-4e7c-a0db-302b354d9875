// Placeholder admin users for build compatibility
export const getUsers = async () => {
  return { data: [], error: null };
};

export const getAllUsers = async () => {
  return { data: [], error: null };
};

export const createUser = async (userData: any) => {
  return { data: null, error: null };
};

export const createInitialAdminUser = async (userData: any) => {
  return { data: null, error: null };
};

export const updateUser = async (id: string, userData: any) => {
  return { data: null, error: null };
};

export const updateUserByAdmin = async (id: string, userData: any) => {
  return { data: null, error: null };
};

export const deleteUser = async (id: string) => {
  return { data: null, error: null };
};