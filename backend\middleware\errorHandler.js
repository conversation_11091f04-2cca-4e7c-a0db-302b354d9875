// backend/middleware/errorHandler.js
module.exports = (err, req, res, next) => {
  console.error('Global error handler:', err);
  if (res.headersSent) return next(err);
  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(409).json({ error: `${field.charAt(0).toUpperCase() + field.slice(1)} already in use.` });
  }
  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const messages = Object.values(err.errors).map(e => e.message);
    return res.status(400).json({ error: messages.join(' ') });
  }
  // File upload error
  if (err.message && err.message.includes('file')) {
    return res.status(400).json({ error: err.message });
  }
  // Default
  res.status(err.status || 500).json({ error: err.message || 'An unexpected error occurred.' });
};
