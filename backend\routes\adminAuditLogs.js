const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');

// GET /api/admin/audit-logs - Get audit logs
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 50, search = '', category = '', severity = '' } = req.query;
    
    // Mock audit logs data - in a real implementation, this would come from a database
    const mockAuditLogs = [
      {
        _id: '1',
        action: 'User Created',
        entity: 'User',
        entityId: 'user123',
        userId: req.user.id,
        userEmail: req.user.email,
        userName: `${req.user.firstName} ${req.user.lastName}`,
        details: { userId: 'user123', email: '<EMAIL>' },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString(),
        severity: 'medium',
        category: 'user'
      },
      {
        _id: '2',
        action: 'Settings Updated',
        entity: 'Global Settings',
        entityId: 'settings1',
        userId: req.user.id,
        userEmail: req.user.email,
        userName: `${req.user.firstName} ${req.user.lastName}`,
        details: { section: 'colors', changes: { primary: '#16A34A' } },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        severity: 'low',
        category: 'settings'
      },
      {
        _id: '3',
        action: 'Payment Processed',
        entity: 'Payment',
        entityId: 'payment123',
        userId: req.user.id,
        userEmail: req.user.email,
        userName: `${req.user.firstName} ${req.user.lastName}`,
        details: { amount: 50000, currency: 'NGN', status: 'success' },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        severity: 'high',
        category: 'payment'
      }
    ];

    // Filter logs based on search and filters
    let filteredLogs = mockAuditLogs;

    if (search) {
      filteredLogs = filteredLogs.filter(log =>
        log.action.toLowerCase().includes(search.toLowerCase()) ||
        log.entity.toLowerCase().includes(search.toLowerCase()) ||
        log.userName.toLowerCase().includes(search.toLowerCase()) ||
        log.userEmail.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (category && category !== 'all') {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    if (severity && severity !== 'all') {
      filteredLogs = filteredLogs.filter(log => log.severity === severity);
    }

    // Pagination
    const pageNumber = parseInt(page);
    const limitNumber = parseInt(limit);
    const startIndex = (pageNumber - 1) * limitNumber;
    const endIndex = startIndex + limitNumber;
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    res.json({
      success: true,
      logs: paginatedLogs,
      pagination: {
        page: pageNumber,
        limit: limitNumber,
        total: filteredLogs.length,
        pages: Math.ceil(filteredLogs.length / limitNumber)
      }
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit logs'
    });
  }
});

// GET /api/admin/audit-logs/export - Export audit logs
router.get('/export', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Mock audit logs data for export
    const auditLogs = [
      {
        timestamp: new Date().toISOString(),
        action: 'User Created',
        entity: 'User',
        user: `${req.user.firstName} ${req.user.lastName}`,
        userEmail: req.user.email,
        severity: 'medium',
        category: 'user',
        ipAddress: req.ip
      }
    ];

    // Convert to CSV format
    const csvHeader = 'Timestamp,Action,Entity,User,User Email,Severity,Category,IP Address\n';
    const csvRows = auditLogs.map(log => 
      `${log.timestamp},"${log.action}","${log.entity}","${log.user}","${log.userEmail}","${log.severity}","${log.category}","${log.ipAddress}"`
    ).join('\n');
    
    const csvContent = csvHeader + csvRows;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=audit-logs-${new Date().toISOString().split('T')[0]}.csv`);
    res.send(csvContent);
  } catch (error) {
    console.error('Error exporting audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export audit logs'
    });
  }
});

module.exports = router; 