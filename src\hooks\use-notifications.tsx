import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { AlertCircle } from 'lucide-react';

export type NotificationType = 'success' | 'error' | 'info' | 'warning';
export type NotificationChannel = 'in-app' | 'email' | 'sms' | 'all';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  timestamp: Date;
  read: boolean;
  channel: NotificationChannel;
  priority?: 'low' | 'medium' | 'high';
  actionUrl?: string;
  userId?: string;
  metadata?: Record<string, any> | null;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  deleteNotification: (id: string) => void;
  filterNotifications: (type?: NotificationType, channel?: NotificationChannel) => Notification[];
  fetchNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  const showNotificationToast = (title: string, message: string, type: NotificationType) => {
    switch (type) {
      case 'success':
        toast.success(title, { description: message });
        break;
      case 'error':
        toast.error(title, { description: message });
        break;
      case 'warning':
        toast(title, {
          description: message,
          icon: <AlertCircle className="h-5 w-5 text-amber-500" />,
        });
        break;
      case 'info':
      default:
        toast.info(title, { description: message });
        break;
    }
  };

  useEffect(() => {
    const count = notifications.filter(notification => !notification.read).length;
    setUnreadCount(count);
  }, [notifications]);

  const fetchNotifications = async () => {
    // Mock implementation - replace with actual API call
    const mockNotifications: Notification[] = [
      {
        id: '1',
        title: 'Deposit Successful',
        message: '₦50,000 has been added to your Flex Savings',
        type: 'success',
        timestamp: new Date(),
        read: false,
        channel: 'in-app',
        priority: 'medium'
      }
    ];
    setNotifications(mockNotifications);
  };

  const addNotification = useCallback(async (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false
    };
    
    setNotifications(prev => [newNotification, ...prev]);
    showNotificationToast(notification.title, notification.message, notification.type);
  }, []);

  const markAsRead = useCallback(async (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  }, []);

  const markAllAsRead = useCallback(async () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  }, []);

  const clearNotifications = useCallback(async () => {
    setNotifications([]);
  }, []);

  const deleteNotification = useCallback(async (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const filterNotifications = useCallback((type?: NotificationType, channel?: NotificationChannel) => {
    let filtered = [...notifications];
    
    if (type) {
      filtered = filtered.filter(notification => notification.type === type);
    }
    
    if (channel) {
      filtered = filtered.filter(notification => 
        notification.channel === channel || notification.channel === 'all'
      );
    }
    
    return filtered;
  }, [notifications]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        deleteNotification,
        filterNotifications,
        fetchNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};