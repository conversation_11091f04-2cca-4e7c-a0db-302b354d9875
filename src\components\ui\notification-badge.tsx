
import * as React from "react"
import { cn } from "@/lib/utils"

interface NotificationBadgeProps {
  count: number
  max?: number
  className?: string
}

export function NotificationBadge({
  count,
  max = 99,
  className,
}: NotificationBadgeProps) {
  const displayCount = count > max ? `${max}+` : count

  if (count <= 0) {
    return null
  }

  return (
    <div
      className={cn(
        "inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-destructive rounded-full dark:bg-red-500 dark:text-white",
        className
      )}
    >
      {displayCount}
    </div>
  )
}
