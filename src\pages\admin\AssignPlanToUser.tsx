
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function AssignPlanToUser() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [savingAmount, setSavingAmount] = useState("");
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [selectedUser, setSelectedUser] = useState("");
  
  // Mock data - would come from API in real implementation
  const savingsPlans = [
    {
      id: "1",
      name: "Daily Saver",
      description: "Start saving with as little as ₦1,000 per day",
      minimumAmount: "1000",
      durationDays: "30",
      interestRate: "5",
    },
    {
      id: "2",
      name: "Weekly Target",
      description: "Achieve your goals with weekly savings",
      minimumAmount: "5000",
      durationDays: "90",
      interestRate: "8",
    },
    {
      id: "3",
      name: "Premium Savings",
      description: "Higher returns for longer-term commitment",
      minimumAmount: "10000",
      durationDays: "180",
      interestRate: "10.5",
    }
  ];
  
  // Mock users data
  const users = [
    { id: "1", name: "John Doe", phoneNumber: "08012345678" },
    { id: "2", name: "Jane Smith", phoneNumber: "08023456789" },
    { id: "3", name: "Samuel Johnson", phoneNumber: "08034567890" },
  ];
  
  const handleDialogOpen = (plan: any) => {
    setSelectedPlan(plan);
    setSavingAmount(plan.minimumAmount);
  };
  
  const handleAssignPlan = () => {
    if (!selectedPlan || !selectedUser) {
      toast({
        title: "Missing information",
        description: "Please select both a user and a plan",
        variant: "destructive"
      });
      return;
    }
    
    const amount = parseFloat(savingAmount);
    const minAmount = parseFloat(selectedPlan.minimumAmount);
    
    if (isNaN(amount) || amount < minAmount) {
      toast({
        title: "Invalid amount",
        description: `Minimum amount is ₦${minAmount.toLocaleString()}`,
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    // Mock API call
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Success",
        description: `Plan assigned to user successfully`,
      });
      setSelectedPlan(null);
      setSelectedUser("");
    }, 1500);
  };

  return (
    <div className="container mx-auto max-w-5xl py-6">
      <h1 className="text-2xl font-bold mb-6">Assign Plans to Users</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {savingsPlans.map((plan) => (
          <Card key={plan.id} className="h-full flex flex-col blue-card">
            <CardHeader>
              <CardTitle className="text-white">{plan.name}</CardTitle>
              <CardDescription className="text-white/80">{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <div className="space-y-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-white">Minimum Amount</p>
                  <p className="text-2xl font-bold text-yellow-300">₦{parseInt(plan.minimumAmount).toLocaleString()}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="text-white/70">Duration</p>
                    <p className="text-white">{plan.durationDays} days</p>
                  </div>
                  <div>
                    <p className="text-white/70">Interest Rate</p>
                    <p className="text-white">{plan.interestRate}%</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="yellow" className="w-full" onClick={() => handleDialogOpen(plan)}>Assign to User</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Assign {selectedPlan?.name}</DialogTitle>
                    <DialogDescription>
                      Assign this savings plan to a user
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="user-select">Select User</Label>
                      <Select value={selectedUser} onValueChange={setSelectedUser}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a user" />
                        </SelectTrigger>
                        <SelectContent>
                          {users.map(user => (
                            <SelectItem key={user.id} value={user.id}>
                              {user.name} ({user.phoneNumber.slice(-10)})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="saving-amount">Initial Savings Amount</Label>
                      <Input
                        id="saving-amount"
                        type="number"
                        value={savingAmount}
                        onChange={(e) => setSavingAmount(e.target.value)}
                        placeholder={`Minimum ₦${selectedPlan?.minimumAmount}`}
                      />
                      <p className="text-sm text-muted-foreground">
                        Minimum amount: ₦{parseInt(selectedPlan?.minimumAmount || "0").toLocaleString()}
                      </p>
                    </div>
                    
                    <div className="bg-muted/50 p-3 rounded-md">
                      <p className="text-sm">
                        Duration: <span className="font-medium">{selectedPlan?.durationDays} days</span><br />
                        Interest Rate: <span className="font-medium">{selectedPlan?.interestRate}%</span>
                      </p>
                    </div>
                  </div>
                  <Button 
                    onClick={handleAssignPlan} 
                    disabled={isLoading} 
                    className="w-full"
                    variant="admin"
                  >
                    {isLoading ? "Processing..." : "Confirm Assignment"}
                  </Button>
                </DialogContent>
              </Dialog>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
