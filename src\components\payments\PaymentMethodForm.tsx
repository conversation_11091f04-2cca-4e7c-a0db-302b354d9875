
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";

interface PaymentMethodFormProps {
  initialData?: {
    name: string;
    accountNumber: string;
    bankName: string;
    instructions: string;
    active: boolean;
  };
  onSave: (data: any) => void;
  isLoading?: boolean;
}

export function PaymentMethodForm({ 
  initialData, 
  onSave, 
  isLoading = false 
}: PaymentMethodFormProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    accountNumber: initialData?.accountNumber || "",
    bankName: initialData?.bankName || "",
    instructions: initialData?.instructions || "",
    active: initialData?.active ?? true
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleToggleActive = () => {
    setFormData(prev => ({ ...prev, active: !prev.active }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.accountNumber || !formData.bankName) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }
    
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Payment Method Name</Label>
        <Input
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder="e.g., Bank Transfer"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="bankName">Bank Name</Label>
        <Input
          id="bankName"
          name="bankName"
          value={formData.bankName}
          onChange={handleChange}
          placeholder="e.g., First Bank of Nigeria"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="accountNumber">Account Number</Label>
        <Input
          id="accountNumber"
          name="accountNumber"
          value={formData.accountNumber}
          onChange={handleChange}
          placeholder="Enter account number"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="instructions">Payment Instructions</Label>
        <Textarea
          id="instructions"
          name="instructions"
          value={formData.instructions}
          onChange={handleChange}
          placeholder="Enter detailed payment instructions for users"
          rows={4}
        />
      </div>
      
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="active"
          checked={formData.active}
          onChange={handleToggleActive}
          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
        />
        <Label htmlFor="active">Active</Label>
      </div>
      
      <Button type="submit" disabled={isLoading} className="w-full bg-brand-blue hover:bg-brand-blue/90">
        {isLoading ? "Saving..." : "Save Payment Method"}
      </Button>
    </form>
  );
}
