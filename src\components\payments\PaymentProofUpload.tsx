
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";

export function PaymentProofUpload({ onSubmit, isLoading = false }) {
  const [formData, setFormData] = useState({
    amount: "",
    reference: "",
    notes: "",
    proofImage: null as File | null,
  });
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({ ...prev, proofImage: file }));
      
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.amount || !formData.reference || !formData.proofImage) {
      toast({
        title: "Missing information",
        description: "Please provide the amount, reference, and payment proof image",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="amount">Amount Paid (₦)</Label>
        <Input
          id="amount"
          name="amount"
          type="number"
          value={formData.amount}
          onChange={handleInputChange}
          placeholder="Enter amount"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="reference">Payment Reference/Transaction ID</Label>
        <Input
          id="reference"
          name="reference"
          value={formData.reference}
          onChange={handleInputChange}
          placeholder="Enter transaction reference"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="notes">Additional Notes</Label>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleInputChange}
          placeholder="Any additional information about your payment"
          rows={3}
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="proofImage">Upload Payment Proof</Label>
        <Input
          id="proofImage"
          name="proofImage"
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="cursor-pointer"
          required
        />
        {previewUrl && (
          <div className="mt-2">
            <p className="text-sm text-muted-foreground mb-1">Preview:</p>
            <img 
              src={previewUrl} 
              alt="Payment proof preview" 
              className="max-h-48 rounded-md border border-gray-200" 
            />
          </div>
        )}
      </div>
      
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? "Submitting..." : "Submit Payment Proof"}
      </Button>
    </form>
  );
}
