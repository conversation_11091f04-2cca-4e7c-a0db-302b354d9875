
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: ["'Open Sans'", "sans-serif"],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        "fintech-red": "hsl(0, 84%, 58%)", /* Primary red */
        "fintech-dark": "hsl(220, 26%, 8%)", /* Dark background */
        "fintech-card": "hsl(220, 20%, 12%)", /* Card background */
        "fintech-accent": "hsl(210, 40%, 20%)", /* Secondary blue */
        "brand-blue": "hsl(213, 82%, 40%)", /* Brand blue */
        "brand-yellow": "hsl(45, 96%, 54%)", /* Brand yellow */
        "brand-lightBlue": "hsl(213, 82%, 60%)", /* Light brand blue */
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "fade-in": {
          "0%": { opacity: 0, transform: "translateY(10px)" },
          "100%": { opacity: 1, transform: "translateY(0)" },
        },
        "pulse-light": {
          "0%, 100%": { opacity: 1 },
          "50%": { opacity: 0.7 },
        },
        "bounce-gentle": {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-5px)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.5s ease-out",
        "pulse-light": "pulse-light 2s infinite",
        "bounce-gentle": "bounce-gentle 2s infinite",
      },
      boxShadow: {
        yellow: "0 4px 14px 0 rgba(253, 227, 20, 0.3)",
        glass: "0 8px 32px rgba(0, 0, 0, 0.1)",
        "glass-strong": "0 10px 40px rgba(0, 0, 0, 0.15)",
        kola: "0 10px 25px -5px rgba(18, 49, 184, 0.1), 0 5px 10px -5px rgba(18, 49, 184, 0.05)",
        "dark-yellow": "0 4px 14px 0 rgba(253, 227, 20, 0.4)",
        "dark-glass": "0 8px 32px rgba(0, 0, 0, 0.3)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
