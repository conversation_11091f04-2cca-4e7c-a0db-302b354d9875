import { useEffect } from 'react';

export const useAOS = () => {
  useEffect(() => {
    // Dynamically import AOS to avoid SSR issues
    const initAOS = async () => {
      try {
        const AOS = await import('aos');
        await import('aos/dist/aos.css');
        
        AOS.default.init({
          duration: 800,
          easing: 'ease-in-out',
          once: true,
          offset: 100,
          delay: 0,
        });
      } catch (error) {
        console.warn('AOS failed to load:', error);
      }
    };

    initAOS();
  }, []);
};