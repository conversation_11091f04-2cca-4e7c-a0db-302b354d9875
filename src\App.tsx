
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Suspense, lazy } from "react";
import { AuthProvider } from "./hooks/use-auth";
import { NotificationProvider } from "./hooks/use-notifications";
import { ThemeProvider } from "./hooks/use-theme";
import { OfflineIndicator } from "./components/ui/offline-indicator";
import ErrorBoundary from "./components/ErrorBoundary";
import { LoadingScreen } from "./components/ui/loading-screen";
import { useAOS } from "./hooks/use-aos";

// Set document title
document.title = "Better Interest | Secure Digital Savings";

// Lazy load components to improve initial load time
const Login = lazy(() => import("./pages/Login"));
const Signup = lazy(() => import("./pages/Signup"));
const NotFound = lazy(() => import("./pages/NotFound"));
const UserDashboard = lazy(() => import("./pages/user/Dashboard"));
const AdminDashboard = lazy(() => import("./pages/admin/Dashboard"));
const UserManagement = lazy(() => import("./pages/admin/UserManagement"));
const UserLayout = lazy(() => import("./components/layout/user-layout").then(module => ({ default: module.UserLayout })));
const SavingsPlans = lazy(() => import("./pages/user/SavingsPlans"));
const Payments = lazy(() => import("./pages/user/Payments"));
const KycVerification = lazy(() => import("./pages/user/KycVerification"));
const Settings = lazy(() => import("./pages/user/Settings"));
const Analytics = lazy(() => import("./pages/user/Analytics"));
const AddCard = lazy(() => import("./pages/user/AddCard"));
const OtpVerificationManagement = lazy(() => import("./pages/admin/OtpVerificationManagement"));
const AssignPlanToUser = lazy(() => import("./pages/admin/AssignPlanToUser"));
const AdminRolesManagement = lazy(() => import("./pages/admin/AdminRolesManagement"));
const Transactions = lazy(() => import("./pages/user/Transactions"));
const AdminAnalytics = lazy(() => import("./pages/admin/Analytics"));
const UserRequests = lazy(() => import("./pages/admin/Requests"));
const UserProfileDetails = lazy(() => import("./pages/admin/UserProfileDetails"));
const StaffManagement = lazy(() => import("./pages/admin/StaffManagement"));
const GroupSavingsPlans = lazy(() => import("./pages/admin/GroupSavingsPlans"));
const NotificationManagement = lazy(() => import("./pages/admin/NotificationManagement"));
const StaffRoleManagement = lazy(() => import("./pages/admin/StaffRoleManagement"));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Add caching options for offline support
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 60 * 60 * 1000, // 1 hour, renamed from cacheTime
      retry: (failureCount, error: any) => {
        // Don't retry if we're offline or there's a 404 error
        if (!navigator.onLine || error?.status === 404) {
          return false;
        }
        return failureCount < 3;
      }
    }
  }
});

const App = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <TooltipProvider>
            <ThemeProvider>
              <AuthProvider>
                <NotificationProvider>
                  <AppContent />
                </NotificationProvider>
              </AuthProvider>
            </ThemeProvider>
          </TooltipProvider>
        </BrowserRouter>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

// Separate component to use hooks safely within React context
const AppContent = () => {
  useAOS(); // Initialize AOS animations safely within React context
  
  return (
    <>
      <Toaster />
      <Sonner />
      <OfflineIndicator />
      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/" element={<Navigate to="/login" replace />} />

          {/* User routes - no longer protected */}
          <Route element={<UserLayout isAdmin={false} />}>
            <Route path="/dashboard" element={<UserDashboard />} />
            <Route path="/savings" element={<SavingsPlans />} />
            <Route path="/payments" element={<Payments />} />
            <Route path="/kyc" element={<KycVerification />} />
            <Route path="/transactions" element={<Transactions />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/profile" element={<Navigate to="/settings" replace />} />
            <Route path="/add-card" element={<AddCard />} />
          </Route>

          {/* Admin routes - no longer protected */}
          <Route element={<UserLayout isAdmin={true} />}>
            <Route path="/admin/dashboard" element={<AdminDashboard />} />
            <Route path="/admin/users" element={<UserManagement />} />
            <Route path="/admin/users/:userId" element={<UserProfileDetails />} />
            <Route path="/admin/settings" element={<Settings />} />
            <Route path="/admin/requests" element={<UserRequests />} />
            <Route path="/admin/analytics" element={<AdminAnalytics />} />
            <Route path="/admin/payment-management" element={<Navigate to="/admin/requests" replace />} />
            <Route path="/admin/verification" element={<OtpVerificationManagement />} />
            <Route path="/admin/assign-plan" element={<AssignPlanToUser />} />
            <Route path="/admin/roles" element={<AdminRolesManagement />} />
            <Route path="/admin/staff" element={<StaffManagement />} />
            <Route path="/admin/group-savings" element={<GroupSavingsPlans />} />
            <Route path="/admin/notifications" element={<NotificationManagement />} />
            <Route path="/admin/staff-roles" element={<StaffRoleManagement />} />
          </Route>

          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </>
  );
};

export default App;
