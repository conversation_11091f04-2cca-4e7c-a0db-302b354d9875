const mongoose = require('mongoose');

const withdrawAccountSchema = new mongoose.Schema({
  bankName: {
    type: String,
    required: true,
  },
  accountNumber: {
    type: String,
    required: true,
    match: /^\d{10}$/, // Ensures a 10-digit account number
  },
  accountName: {
    type: String,
    required: true,
  },
  accountType: {
    type: String,
    required: true,
    enum: ['Savings', 'Current'], // Example account types
  },
  isDefault: {
    type: Boolean,
    default: false, // Default value is false
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true, // Adds createdAt and updatedAt fields
});

module.exports = mongoose.model('WithdrawAccount', withdrawAccountSchema);