
import { <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { PayoutModeSettings, PayoutSettings } from "@/components/payments/PayoutModeSettings";
import { toast } from "sonner";
import { useState } from "react";

interface PayoutModeDialogProps {
  onClose: () => void;
}

export function PayoutModeDialog({ onClose }: PayoutModeDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Get saved payout settings from localStorage
  const getSavedPayoutSettings = (): PayoutSettings | undefined => {
    const settings = localStorage.getItem('user_payout_settings');
    if (settings) {
      return JSON.parse(settings);
    }
    return undefined;
  };

  const handleSubmit = async (data: PayoutSettings) => {
    try {
      setIsLoading(true);
      // In a real app, this would be an API call
      // For now, we'll save to localStorage
      localStorage.setItem('user_payout_settings', JSON.stringify(data));
      
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast.success("Payout settings saved successfully");
      onClose();
    } catch (error) {
      console.error('Error saving payout settings:', error);
      toast.error('Failed to save payout settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DialogContent className="sm:max-w-[500px] dark:bg-gray-900 dark:border-gray-800 dark-transition">
      <DialogHeader>
        <DialogTitle className="dark:text-gray-200">Set Preferred Payout Method</DialogTitle>
      </DialogHeader>
      <PayoutModeSettings 
        onSubmit={handleSubmit}
        isLoading={isLoading}
        defaultValues={getSavedPayoutSettings()}
        currencySymbol="₦"
      />
    </DialogContent>
  );
}
