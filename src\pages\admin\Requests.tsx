import { useState } from "react";
import {
  Ban,
  Check,
  ChevronsUpDown,
  Edit,
  FileEdit,
  Key,
  Lock,
  Mail,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  Shield,
  Trash,
  User,
  UserCog,
  UserPlus,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  <PERSON>alogTit<PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

const requests = [
  {
    id: 1,
    type: "Withdrawal",
    user: {
      name: "Chioma Okafor",
      email: "<EMAIL>",
      avatar: null
    },
    amount: 50000,
    status: "Pending",
    date: "2024-02-15",
    details: "Withdrawal request for savings plan"
  },
  {
    id: 2,
    type: "Plan Change",
    user: {
      name: "Emeka Eze",
      email: "<EMAIL>",
      avatar: null
    },
    amount: null,
    status: "Approved",
    date: "2024-02-14",
    details: "Request to upgrade savings plan"
  },
  {
    id: 3,
    type: "KYC Update",
    user: {
      name: "Funke Adebayo",
      email: "<EMAIL>",
      avatar: null
    },
    amount: null,
    status: "Pending",
    date: "2024-02-13",
    details: "Updated identification documents"
  }
];

const UserRequests = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("All");
  const [statusFilter, setStatusFilter] = useState("All");
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  const filteredRequests = requests.filter((request) => {
    const matchesSearch =
      request.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.type.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = typeFilter === "All" || request.type === typeFilter;
    const matchesStatus = statusFilter === "All" || request.status === statusFilter;

    return matchesSearch && matchesType && matchesStatus;
  });

  const handleApproveRequest = (requestId: number) => {
    toast.success("Request approved successfully");
  };

  const handleRejectRequest = (requestId: number) => {
    toast.success("Request rejected successfully");
  };

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request);
    setIsDetailsDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">User Requests</h2>
          <p className="text-muted-foreground">
            Manage and process user requests
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Requests</CardTitle>
          <CardDescription>
            View and manage pending user requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search requests..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select
                value={typeFilter}
                onValueChange={setTypeFilter}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Request Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Types</SelectItem>
                  <SelectItem value="Withdrawal">Withdrawal</SelectItem>
                  <SelectItem value="Plan Change">Plan Change</SelectItem>
                  <SelectItem value="KYC Update">KYC Update</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Status</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="Approved">Approved</SelectItem>
                  <SelectItem value="Rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarFallback>
                            {request.user.name.split(" ").map((n: string) => n[0]).join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{request.user.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {request.user.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{request.type}</Badge>
                    </TableCell>
                    <TableCell>
                      {request.amount ? `₦${request.amount.toLocaleString()}` : "-"}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          request.status === "Approved"
                            ? "default"
                            : request.status === "Rejected"
                            ? "destructive"
                            : "secondary"
                        }
                      >
                        {request.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{request.date}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleViewDetails(request)}>
                            <FileEdit className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          {request.status === "Pending" && (
                            <>
                              <DropdownMenuItem onClick={() => handleApproveRequest(request.id)}>
                                <Check className="mr-2 h-4 w-4" />
                                Approve
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleRejectRequest(request.id)}>
                                <X className="mr-2 h-4 w-4" />
                                Reject
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Request Details</DialogTitle>
            <DialogDescription>
              View complete request information
            </DialogDescription>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-4">
              <div>
                <Label>User</Label>
                <p className="text-sm">{selectedRequest.user.name}</p>
                <p className="text-sm text-muted-foreground">{selectedRequest.user.email}</p>
              </div>
              <div>
                <Label>Request Type</Label>
                <p className="text-sm">{selectedRequest.type}</p>
              </div>
              {selectedRequest.amount && (
                <div>
                  <Label>Amount</Label>
                  <p className="text-sm">₦{selectedRequest.amount.toLocaleString()}</p>
                </div>
              )}
              <div>
                <Label>Status</Label>
                <Badge
                  variant={
                    selectedRequest.status === "Approved"
                      ? "default"
                      : selectedRequest.status === "Rejected"
                      ? "destructive"
                      : "secondary"
                  }
                  className="mt-1"
                >
                  {selectedRequest.status}
                </Badge>
              </div>
              <div>
                <Label>Date</Label>
                <p className="text-sm">{selectedRequest.date}</p>
              </div>
              <div>
                <Label>Details</Label>
                <p className="text-sm">{selectedRequest.details}</p>
              </div>
            </div>
          )}
          <DialogFooter>
            {selectedRequest?.status === "Pending" && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleRejectRequest(selectedRequest.id)}
                >
                  Reject
                </Button>
                <Button
                  onClick={() => handleApproveRequest(selectedRequest.id)}
                >
                  Approve
                </Button>
              </div>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserRequests;
