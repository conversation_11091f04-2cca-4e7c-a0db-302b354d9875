
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { KeyRound } from "lucide-react";

export function ChangePinForm() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    currentPin: "",
    newPin: "",
    confirmPin: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // Only allow numeric input with max length 4
    if (/^\d*$/.test(value) && value.length <= 4) {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (formData.newPin.length !== 4) {
      toast({
        title: "Invalid PIN",
        description: "PIN must be exactly 4 digits",
        variant: "destructive",
      });
      return;
    }
    
    if (formData.newPin !== formData.confirmPin) {
      toast({
        title: "PINs don't match",
        description: "New PIN and confirm PIN must match",
        variant: "destructive",
      });
      return;
    }
    
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      toast({
        title: "PIN updated",
        description: "Your transaction PIN has been changed successfully",
      });
      
      // Reset form
      setFormData({
        currentPin: "",
        newPin: "",
        confirmPin: "",
      });
    }, 1000);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <KeyRound className="h-5 w-5 mr-2" />
          Change Transaction PIN
        </CardTitle>
        <CardDescription>
          Update your 4-digit transaction PIN
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentPin">Current PIN</Label>
            <Input
              id="currentPin"
              name="currentPin"
              type="password"
              value={formData.currentPin}
              onChange={handleChange}
              required
              maxLength={4}
              className="text-center tracking-widest"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="newPin">New PIN</Label>
            <Input
              id="newPin"
              name="newPin"
              type="password"
              value={formData.newPin}
              onChange={handleChange}
              required
              maxLength={4}
              className="text-center tracking-widest"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="confirmPin">Confirm New PIN</Label>
            <Input
              id="confirmPin"
              name="confirmPin"
              type="password"
              value={formData.confirmPin}
              onChange={handleChange}
              required
              maxLength={4}
              className="text-center tracking-widest"
            />
          </div>
          
          <Button 
            type="submit" 
            className="bg-brand-blue hover:bg-brand-blue/90" 
            disabled={loading}
          >
            {loading ? "Updating..." : "Update PIN"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
