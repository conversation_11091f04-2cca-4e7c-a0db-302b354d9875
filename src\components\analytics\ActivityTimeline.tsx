import { FintechCard } from '@/components/ui/fintech-card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Clock, TrendingUp, TrendingDown, DollarSign, CreditCard } from 'lucide-react';

interface Activity {
  id: string;
  type: 'deposit' | 'withdrawal' | 'savings' | 'loan' | 'investment';
  user: string;
  amount: number;
  description: string;
  time: string;
  status: 'completed' | 'pending' | 'failed';
}

const mockActivities: Activity[] = [
  {
    id: '1',
    type: 'deposit',
    user: '<PERSON>',
    amount: 150000,
    description: 'Wallet funding via card',
    time: '2 hours ago',
    status: 'completed'
  },
  {
    id: '2',
    type: 'savings',
    user: '<PERSON>',
    amount: 50000,
    description: 'Fixed deposit creation',
    time: '4 hours ago',
    status: 'completed'
  },
  {
    id: '3',
    type: 'withdrawal',
    user: '<PERSON>',
    amount: 80000,
    description: 'Bank transfer withdrawal',
    time: '6 hours ago',
    status: 'pending'
  },
  {
    id: '4',
    type: 'investment',
    user: '<PERSON>',
    amount: 200000,
    description: 'Treasury bills purchase',
    time: '8 hours ago',
    status: 'completed'
  }
];

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'deposit':
      return <TrendingUp className="h-4 w-4 text-success" />;
    case 'withdrawal':
      return <TrendingDown className="h-4 w-4 text-destructive" />;
    case 'savings':
      return <DollarSign className="h-4 w-4 text-primary" />;
    case 'investment':
      return <CreditCard className="h-4 w-4 text-accent-foreground" />;
    default:
      return <Clock className="h-4 w-4 text-muted-foreground" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-success/10 text-success border-success/20';
    case 'pending':
      return 'bg-yellow-500/10 text-yellow-600 border-yellow-500/20';
    case 'failed':
      return 'bg-destructive/10 text-destructive border-destructive/20';
    default:
      return 'bg-muted text-muted-foreground';
  }
};

export const ActivityTimeline = () => {
  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Recent Activity</h3>
        <Badge variant="outline" className="text-xs">Live</Badge>
      </div>
      
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {mockActivities.map((activity) => (
          <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
            <div className="flex-shrink-0">
              <Avatar className="h-8 w-8">
                <AvatarImage src="" />
                <AvatarFallback className="text-xs bg-primary/10">
                  {activity.user.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                {getActivityIcon(activity.type)}
                <p className="text-sm font-medium truncate">{activity.user}</p>
                <Badge className={`text-xs ${getStatusColor(activity.status)}`}>
                  {activity.status}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">{activity.description}</p>
            </div>
            
            <div className="flex-shrink-0 text-right">
              <p className="text-sm font-medium">₦{activity.amount.toLocaleString()}</p>
              <p className="text-xs text-muted-foreground">{activity.time}</p>
            </div>
          </div>
        ))}
      </div>
    </FintechCard>
  );
};