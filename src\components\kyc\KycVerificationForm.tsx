
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";

export function KycVerificationForm({ onSubmit, isLoading = false }) {
  const [formData, setFormData] = useState({
    fullName: "",
    phoneNumber: "",
    email: "",
    address: "",
    city: "",
    state: "",
    idType: "nin",
    idNumber: "",
    idImage: null as File | null,
    proofOfAddressImage: null as File | null,
  });
  
  const [previews, setPreviews] = useState({
    idImage: null as string | null,
    proofOfAddressImage: null as string | null
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const { name } = e.target;
      const file = e.target.files[0];
      setFormData(prev => ({ ...prev, [name]: file }));
      
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviews(prev => ({
          ...prev,
          [name]: reader.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const requiredFields = [
      'fullName', 'phoneNumber', 'email', 'address', 
      'city', 'state', 'idType', 'idNumber'
    ];
    
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);
    
    if (missingFields.length > 0 || !formData.idImage || !formData.proofOfAddressImage) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields and upload the required documents",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-muted/50 p-3 rounded-md mb-4">
        <h3 className="font-medium">Personal Information</h3>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="fullName">Full Name</Label>
        <Input
          id="fullName"
          name="fullName"
          value={formData.fullName}
          onChange={handleInputChange}
          placeholder="Enter your full name"
          required
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="phoneNumber">Phone Number</Label>
          <Input
            id="phoneNumber"
            name="phoneNumber"
            value={formData.phoneNumber}
            onChange={handleInputChange}
            placeholder="Enter phone number"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Enter email address"
            required
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="address">Residential Address</Label>
        <Textarea
          id="address"
          name="address"
          value={formData.address}
          onChange={handleInputChange}
          placeholder="Enter your full address"
          rows={2}
          required
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">City</Label>
          <Input
            id="city"
            name="city"
            value={formData.city}
            onChange={handleInputChange}
            placeholder="Enter city"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="state">State</Label>
          <Input
            id="state"
            name="state"
            value={formData.state}
            onChange={handleInputChange}
            placeholder="Enter state"
            required
          />
        </div>
      </div>
      
      <div className="bg-muted/50 p-3 rounded-md mt-6 mb-4">
        <h3 className="font-medium">Identification Information</h3>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="idType">ID Type</Label>
        <Select 
          onValueChange={(value) => handleSelectChange("idType", value)} 
          defaultValue={formData.idType}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select ID type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="nin">National Identity Number (NIN)</SelectItem>
            <SelectItem value="bvn">Bank Verification Number (BVN)</SelectItem>
            <SelectItem value="drivers_license">Driver's License</SelectItem>
            <SelectItem value="passport">International Passport</SelectItem>
            <SelectItem value="voters_card">Voter's Card</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="idNumber">ID Number</Label>
        <Input
          id="idNumber"
          name="idNumber"
          value={formData.idNumber}
          onChange={handleInputChange}
          placeholder="Enter your ID number"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="idImage">Upload ID Document</Label>
        <Input
          id="idImage"
          name="idImage"
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="cursor-pointer"
          required
        />
        {previews.idImage && (
          <div className="mt-2">
            <p className="text-sm text-muted-foreground mb-1">Preview:</p>
            <img 
              src={previews.idImage} 
              alt="ID document preview" 
              className="max-h-48 rounded-md border border-gray-200" 
            />
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="proofOfAddressImage">Upload Proof of Address</Label>
        <p className="text-sm text-muted-foreground mb-2">
          (Utility bill, bank statement, etc. not older than 3 months)
        </p>
        <Input
          id="proofOfAddressImage"
          name="proofOfAddressImage"
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="cursor-pointer"
          required
        />
        {previews.proofOfAddressImage && (
          <div className="mt-2">
            <p className="text-sm text-muted-foreground mb-1">Preview:</p>
            <img 
              src={previews.proofOfAddressImage} 
              alt="Proof of address preview" 
              className="max-h-48 rounded-md border border-gray-200" 
            />
          </div>
        )}
      </div>
      
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? "Submitting..." : "Submit KYC Information"}
      </Button>
    </form>
  );
}
