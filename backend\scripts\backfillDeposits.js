// Script to backfill old approved deposits into the Transaction model
const mongoose = require('mongoose');
const Deposit = require('../models/deposit');
const Transaction = require('../models/transaction');
const User = require('../models/user');

const MONGO_URI = 'mongodb+srv://kojaonlineltd:<EMAIL>/?retryWrites=true&w=majority&appName=asusuamac'; // Change if needed

// Helper to generate a 6-character alphanumeric reference
function generateReference() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let ref = '';
  for (let i = 0; i < 6; i++) {
    ref += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return ref;
}

async function backfillDeposits() {
  await mongoose.connect(MONGO_URI);
  const deposits = await Deposit.find({ status: 'approved' });
  let created = 0, skipped = 0;
  for (const deposit of deposits) {
    // Check if transaction already exists for this deposit (by user, amount, and date)
    const exists = await Transaction.findOne({
      userId: deposit.userId,
      amount: deposit.amount,
      type: 'deposit',
      date: deposit.createdAt,
    });
    if (exists) {
      skipped++;
      continue;
    }
    // Get user's balance after this deposit (best effort, may not be exact for old data)
    const user = await User.findById(deposit.userId);
    // Generate unique reference
    let reference;
    let refExists = true;
    while (refExists) {
      reference = generateReference();
      refExists = await Transaction.exists({ reference });
    }
    await Transaction.create({
      userId: deposit.userId,
      type: 'deposit',
      amount: deposit.amount,
      description: `Deposit via ${deposit.bankName} (${deposit.accountNumber})`,
      date: deposit.createdAt,
      balanceAfter: user ? user.balance : 0,
      reference,
    });
    created++;
  }
  console.log(`Backfill complete. Created: ${created}, Skipped (already exists): ${skipped}`);
  await mongoose.disconnect();
}

backfillDeposits().catch(err => {
  console.error('Backfill error:', err);
  process.exit(1);
});
