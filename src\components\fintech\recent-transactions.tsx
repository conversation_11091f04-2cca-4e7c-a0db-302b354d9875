import React from "react";
import { FintechCard } from "@/components/ui/fintech-card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ArrowUpRight, ArrowDownLeft, TrendingUp, ShoppingCart, Smartphone, Zap } from "lucide-react";

interface Transaction {
  id: string;
  type: "debit" | "credit" | "investment" | "savings";
  title: string;
  description: string;
  amount: number;
  timestamp: string;
  status: "completed" | "pending" | "failed";
  category?: string;
}

interface RecentTransactionsProps {
  transactions: Transaction[];
  onViewAll?: () => void;
}

export function RecentTransactions({ transactions, onViewAll }: RecentTransactionsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString("en-NG", { month: "short", day: "numeric" });
    }
  };

  const getTransactionIcon = (type: string, category?: string) => {
    switch (type) {
      case "credit":
        return ArrowDownLeft;
      case "debit":
        if (category === "shopping") return ShoppingCart;
        if (category === "bills") return Smartphone;
        if (category === "utilities") return Zap;
        return ArrowUpRight;
      case "investment":
        return TrendingUp;
      case "savings":
        return TrendingUp;
      default:
        return ArrowUpRight;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "default";
      case "pending":
        return "secondary";
      case "failed":
        return "destructive";
      default:
        return "default";
    }
  };

  const getAmountColor = (type: string) => {
    switch (type) {
      case "credit":
      case "investment":
      case "savings":
        return "text-green-600";
      case "debit":
        return "text-red-600";
      default:
        return "text-foreground";
    }
  };

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold">Recent Transactions</h3>
        <button
          onClick={onViewAll}
          className="text-sm text-primary hover:underline"
        >
          View All
        </button>
      </div>

      <div className="space-y-3">
        {transactions.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-muted-foreground text-sm">
              No transactions yet
            </div>
          </div>
        ) : (
          transactions.slice(0, 5).map((transaction) => {
            const Icon = getTransactionIcon(transaction.type, transaction.category);
            return (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-primary/10">
                    <Icon className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">{transaction.title}</p>
                    <div className="flex items-center gap-2">
                      <p className="text-xs text-muted-foreground">
                        {transaction.description}
                      </p>
                      <Badge
                        variant={getStatusColor(transaction.status) as any}
                        className="text-xs"
                      >
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-semibold text-sm ${getAmountColor(transaction.type)}`}>
                    {transaction.type === "credit" ? "+" : "-"}
                    {formatCurrency(Math.abs(transaction.amount))}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatTime(transaction.timestamp)}
                  </p>
                </div>
              </div>
            );
          })
        )}
      </div>
    </FintechCard>
  );
}