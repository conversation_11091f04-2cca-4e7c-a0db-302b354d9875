
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { CreditCard, Building2, User, CircleDollarSign } from "lucide-react";
import { useBalance } from "@/hooks/use-balance";

interface WithdrawalRequestFormProps {
  onSubmit: (data: any) => void;
  isLoading?: boolean;
}

export function WithdrawalRequestForm({ 
  onSubmit, 
  isLoading = false
}: WithdrawalRequestFormProps) {
  const { balance, updateBalance } = useBalance();
  const [formData, setFormData] = useState({
    amount: "",
    bankName: "",
    accountNumber: "",
    accountName: "",
    reason: "",
  });

  // Load saved payout settings if available
  React.useEffect(() => {
    const savedSettings = localStorage.getItem('user_payout_settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      if (settings.payoutMode === 'bank_transfer') {
        setFormData(prev => ({
          ...prev,
          bankName: settings.bankName || '',
          accountNumber: settings.accountNumber || '',
          accountName: settings.accountName || ''
        }));
      }
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(formData.amount);
    
    if (amount <= 0) {
      toast.error("Please enter a valid withdrawal amount");
      return;
    }
    
    if (amount > balance) {
      toast.error("Your withdrawal amount exceeds your available balance");
      return;
    }
    
    if (!formData.bankName || !formData.accountNumber || !formData.accountName) {
      toast.error("Please provide all required banking details");
      return;
    }
    
    // Update the balance
    const success = await updateBalance(amount, 'subtract');
    
    if (!success) {
      return;
    }
    
    // Create a transaction record in localStorage
    const transactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
    transactions.push({
      id: `txn-${Date.now()}`,
      type: 'withdrawal',
      amount: amount,
      date: new Date().toISOString().split('T')[0],
      status: 'pending',
      reference: `WIT${Math.floor(Math.random() * 100000)}`
    });
    localStorage.setItem('user_transactions', JSON.stringify(transactions));
    
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-brand-blue text-brand-yellow p-4 rounded-md mb-4 border border-brand-yellow/30 shadow-yellow">
        <div className="flex items-center gap-2">
          <CircleDollarSign className="h-5 w-5" />
          <p className="font-medium">Available Balance: ₦{balance.toLocaleString()}</p>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="amount" className="flex items-center gap-1">
          <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
          Withdrawal Amount (₦)
        </Label>
        <Input
          id="amount"
          name="amount"
          type="number"
          value={formData.amount}
          onChange={handleChange}
          placeholder="Enter amount to withdraw"
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="bankName" className="flex items-center gap-1">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          Bank Name
        </Label>
        <Input
          id="bankName"
          name="bankName"
          value={formData.bankName}
          onChange={handleChange}
          placeholder="Enter your bank name"
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="accountNumber" className="flex items-center gap-1">
          <CreditCard className="h-4 w-4 text-muted-foreground" />
          Account Number
        </Label>
        <Input
          id="accountNumber"
          name="accountNumber"
          value={formData.accountNumber}
          onChange={handleChange}
          placeholder="Enter your account number"
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="accountName" className="flex items-center gap-1">
          <User className="h-4 w-4 text-muted-foreground" />
          Account Name
        </Label>
        <Input
          id="accountName"
          name="accountName"
          value={formData.accountName}
          onChange={handleChange}
          placeholder="Enter account holder name"
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="reason">Reason for Withdrawal (Optional)</Label>
        <Textarea
          id="reason"
          name="reason"
          value={formData.reason}
          onChange={handleChange}
          placeholder="Why are you making this withdrawal?"
          className="border-brand-blue/20 focus:border-brand-yellow focus:ring-brand-yellow/30"
          rows={3}
        />
      </div>
      
      <Button 
        type="submit" 
        disabled={isLoading} 
        variant="brand"
        className="w-full shadow-yellow"
      >
        {isLoading ? "Processing..." : "Submit Withdrawal Request"}
      </Button>
    </form>
  );
}
