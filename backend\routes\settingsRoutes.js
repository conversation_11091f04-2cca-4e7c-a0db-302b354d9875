const express = require('express');
const router = express.Router();
const Settings = require('../models/settings');
const { authenticateToken } = require('../middleware/authMiddleware');

// Get global withdrawal penalty
router.get('/withdrawal-penalty', async (req, res) => {
  try {
    let setting = await Settings.findOne({ key: 'withdrawalPenalty' });
    if (!setting) {
      // Default to 3% if not set
      setting = await Settings.create({ key: 'withdrawalPenalty', value: 3 });
    }
    res.json({ penalty: setting.value });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch withdrawal penalty' });
  }
});

// Update global withdrawal penalty (admin only)
router.put('/withdrawal-penalty', authenticateToken, async (req, res) => {
  try {
    const { penalty } = req.body;
    if (typeof penalty !== 'number' || penalty < 0 || penalty > 100) {
      return res.status(400).json({ error: 'Penalty must be a number between 0 and 100' });
    }
    let setting = await Settings.findOneAndUpdate(
      { key: 'withdrawalPenalty' },
      { value: penalty },
      { new: true, upsert: true }
    );
    res.json({ penalty: setting.value });
  } catch (err) {
    res.status(500).json({ error: 'Failed to update withdrawal penalty' });
  }
});


// --- PLAN-SPECIFIC INTEREST RATE ENDPOINTS ---

// Helper: get key for plan type
function getPlanInterestKey(planType) {
  return `interestRate_${planType}`;
}

// Get interest rate for a specific plan type
router.get('/plan-interest-rate/:planType', async (req, res) => {
  const { planType } = req.params;
  if (!planType) return res.status(400).json({ error: 'planType is required' });
  try {
    let setting = await Settings.findOne({ key: getPlanInterestKey(planType) });
    if (!setting) {
      // No custom rate set, return null (frontend should fall back to global)
      return res.json({ rate: null });
    }
    res.json({ rate: setting.value });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch plan interest rate' });
  }
});

// Update interest rate for a specific plan type (admin only)
router.put('/plan-interest-rate/:planType', authenticateToken, async (req, res) => {
  const { planType } = req.params;
  const { rate } = req.body;
  if (!planType) return res.status(400).json({ error: 'planType is required' });
  if (typeof rate !== 'number' || rate < 0 || rate > 100) {
    return res.status(400).json({ error: 'Rate must be a number between 0 and 100' });
  }
  try {
    let setting = await Settings.findOneAndUpdate(
      { key: getPlanInterestKey(planType) },
      { value: rate },
      { new: true, upsert: true }
    );
    res.json({ rate: setting.value });
  } catch (err) {
    res.status(500).json({ error: 'Failed to update plan interest rate' });
  }
});

module.exports = router;

// --- GLOBAL INTEREST RATE ENDPOINTS ---

// Get global interest rate (per annum)
router.get('/global-interest-rate', async (req, res) => {
  try {
    let setting = await Settings.findOne({ key: 'globalInterestRate' });
    if (!setting) {
      // Default to 12% if not set
      setting = await Settings.create({ key: 'globalInterestRate', value: 12 });
    }
    res.json({ rate: setting.value });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch global interest rate' });
  }
});

// Update global interest rate (admin only)
router.put('/global-interest-rate', authenticateToken, async (req, res) => {
  try {
    const { rate } = req.body;
    if (typeof rate !== 'number' || rate < 0 || rate > 100) {
      return res.status(400).json({ error: 'Rate must be a number between 0 and 100' });
    }
    let setting = await Settings.findOneAndUpdate(
      { key: 'globalInterestRate' },
      { value: rate },
      { new: true, upsert: true }
    );
    res.json({ rate: setting.value });
  } catch (err) {
    res.status(500).json({ error: 'Failed to update global interest rate' });
  }
});
