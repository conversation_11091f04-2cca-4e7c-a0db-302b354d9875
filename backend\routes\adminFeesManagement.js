const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');
const Transaction = require('../models/transaction');
const User = require('../models/user');

// Mock fee structure data - in a real app, this would be stored in a database
let feeStructures = [
  {
    id: "1",
    name: "Withdrawal Fee",
    description: "Standard fee for withdrawals",
    feeType: "fixed",
    amount: 100,
    percentage: 0,
    minimumAmount: 0,
    maximumAmount: 1000,
    isActive: true,
    category: "withdrawal",
    appliesTo: "all",
    conditions: "Applies to all withdrawal amounts",
    gracePeriod: "0",
    autoApply: true,
    refundable: false,
    taxInclusive: true,
    totalCollected: 45000,
    transactionsCount: 450,
    createdAt: "2024-01-15"
  },
  {
    id: "2",
    name: "Transfer Fee",
    description: "Fee for inter-bank transfers",
    feeType: "percentage",
    amount: 0,
    percentage: 0.5,
    minimumAmount: 50,
    maximumAmount: 500,
    isActive: true,
    category: "transfer",
    appliesTo: "interbank",
    conditions: "Only for inter-bank transfers",
    gracePeriod: "0",
    autoApply: true,
    refundable: false,
    taxInclusive: true,
    totalCollected: 125000,
    transactionsCount: 250,
    createdAt: "2024-02-10"
  },
  {
    id: "3",
    name: "Maintenance Fee",
    description: "Monthly account maintenance fee",
    feeType: "fixed",
    amount: 500,
    percentage: 0,
    minimumAmount: 0,
    maximumAmount: 500,
    isActive: true,
    category: "maintenance",
    appliesTo: "inactive",
    conditions: "Applies to accounts with no activity for 3 months",
    gracePeriod: "90",
    autoApply: true,
    refundable: false,
    taxInclusive: true,
    totalCollected: 75000,
    transactionsCount: 150,
    createdAt: "2024-03-05"
  },
  {
    id: "4",
    name: "KYC Verification",
    description: "Fee for identity verification services",
    feeType: "fixed",
    amount: 2000,
    percentage: 0,
    minimumAmount: 0,
    maximumAmount: 2000,
    isActive: true,
    category: "service",
    appliesTo: "new_users",
    conditions: "One-time fee for new user verification",
    gracePeriod: "0",
    autoApply: false,
    refundable: true,
    taxInclusive: true,
    totalCollected: 180000,
    transactionsCount: 90,
    createdAt: "2024-01-20"
  },
  {
    id: "5",
    name: "Express Processing",
    description: "Fee for expedited transaction processing",
    feeType: "fixed",
    amount: 500,
    percentage: 0,
    minimumAmount: 0,
    maximumAmount: 500,
    isActive: true,
    category: "service",
    appliesTo: "express",
    conditions: "For transactions processed within 2 hours",
    gracePeriod: "0",
    autoApply: false,
    refundable: false,
    taxInclusive: true,
    totalCollected: 45000,
    transactionsCount: 90,
    createdAt: "2024-02-15"
  },
  {
    id: "6",
    name: "Late Payment Penalty",
    description: "Penalty for late loan payments",
    feeType: "percentage",
    amount: 0,
    percentage: 2.5,
    minimumAmount: 1000,
    maximumAmount: 5000,
    isActive: true,
    category: "penalty",
    appliesTo: "loan_defaulters",
    conditions: "Applies to loan payments overdue by 30+ days",
    gracePeriod: "30",
    autoApply: true,
    refundable: false,
    taxInclusive: false,
    totalCollected: 25000,
    transactionsCount: 25,
    createdAt: "2024-01-10"
  }
];

// Get all fee structures
router.get('/fees', authenticateToken, async (req, res) => {
  try {
    const { category } = req.query;
    
    let filteredFees = feeStructures;
    if (category && category !== 'all') {
      filteredFees = feeStructures.filter(fee => fee.category === category);
    }
    
    res.json(filteredFees);
  } catch (error) {
    console.error('Error fetching fees:', error);
    res.status(500).json({ error: 'Failed to fetch fees' });
  }
});

// Create new fee structure
router.post('/fees', authenticateToken, async (req, res) => {
  try {
    const {
      name,
      description,
      feeType,
      amount,
      percentage,
      minimumAmount,
      maximumAmount,
      isActive,
      category,
      appliesTo,
      conditions,
      gracePeriod,
      autoApply,
      refundable,
      taxInclusive
    } = req.body;

    const newFee = {
      id: (feeStructures.length + 1).toString(),
      name,
      description,
      feeType,
      amount: parseFloat(amount) || 0,
      percentage: parseFloat(percentage) || 0,
      minimumAmount: parseFloat(minimumAmount) || 0,
      maximumAmount: parseFloat(maximumAmount) || 0,
      isActive,
      category,
      appliesTo,
      conditions,
      gracePeriod: gracePeriod.toString(),
      autoApply,
      refundable,
      taxInclusive,
      totalCollected: 0,
      transactionsCount: 0,
      createdAt: new Date().toISOString().split('T')[0]
    };

    feeStructures.push(newFee);
    res.status(201).json({ message: 'Fee structure created successfully', fee: newFee });
  } catch (error) {
    console.error('Error creating fee structure:', error);
    res.status(500).json({ error: 'Failed to create fee structure' });
  }
});

// Update fee structure
router.put('/fees/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const feeIndex = feeStructures.findIndex(fee => fee.id === id);
    if (feeIndex === -1) {
      return res.status(404).json({ error: 'Fee structure not found' });
    }

    feeStructures[feeIndex] = {
      ...feeStructures[feeIndex],
      ...updateData,
      amount: parseFloat(updateData.amount) || feeStructures[feeIndex].amount,
      percentage: parseFloat(updateData.percentage) || feeStructures[feeIndex].percentage,
      minimumAmount: parseFloat(updateData.minimumAmount) || feeStructures[feeIndex].minimumAmount,
      maximumAmount: parseFloat(updateData.maximumAmount) || feeStructures[feeIndex].maximumAmount
    };

    res.json({ message: 'Fee structure updated successfully', fee: feeStructures[feeIndex] });
  } catch (error) {
    console.error('Error updating fee structure:', error);
    res.status(500).json({ error: 'Failed to update fee structure' });
  }
});

// Delete fee structure
router.delete('/fees/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const feeIndex = feeStructures.findIndex(fee => fee.id === id);
    
    if (feeIndex === -1) {
      return res.status(404).json({ error: 'Fee structure not found' });
    }

    feeStructures.splice(feeIndex, 1);
    res.json({ message: 'Fee structure deleted successfully' });
  } catch (error) {
    console.error('Error deleting fee structure:', error);
    res.status(500).json({ error: 'Failed to delete fee structure' });
  }
});

// Get fee transactions
router.get('/transactions', authenticateToken, async (req, res) => {
  try {
    // Mock fee transactions - in a real app, this would come from the database
    const feeTransactions = [
      {
        id: "1",
        userId: "U001",
        userName: "John Doe",
        feeName: "Withdrawal Fee",
        amount: 100,
        transactionType: "withdrawal",
        status: "completed",
        date: "2024-01-15",
        description: "Withdrawal fee for ₦50,000"
      },
      {
        id: "2",
        userId: "U002",
        userName: "Jane Smith",
        feeName: "Transfer Fee",
        amount: 250,
        transactionType: "transfer",
        status: "pending",
        date: "2024-01-15",
        description: "Inter-bank transfer fee for ₦50,000"
      },
      {
        id: "3",
        userId: "U003",
        userName: "Mike Johnson",
        feeName: "KYC Verification",
        amount: 2000,
        transactionType: "service",
        status: "completed",
        date: "2024-01-14",
        description: "Identity verification service fee"
      },
      {
        id: "4",
        userId: "U004",
        userName: "Sarah Wilson",
        feeName: "Maintenance Fee",
        amount: 500,
        transactionType: "maintenance",
        status: "completed",
        date: "2024-01-13",
        description: "Monthly account maintenance fee"
      },
      {
        id: "5",
        userId: "U005",
        userName: "David Brown",
        feeName: "Express Processing",
        amount: 500,
        transactionType: "service",
        status: "completed",
        date: "2024-01-12",
        description: "Express processing fee for urgent transfer"
      }
    ];

    res.json(feeTransactions);
  } catch (error) {
    console.error('Error fetching fee transactions:', error);
    res.status(500).json({ error: 'Failed to fetch fee transactions' });
  }
});

// Get fee analytics
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    // Calculate analytics from fee structures
    const totalCollected = feeStructures.reduce((sum, fee) => sum + fee.totalCollected, 0);
    const activeFees = feeStructures.filter(fee => fee.isActive).length;
    const totalTransactions = feeStructures.reduce((sum, fee) => sum + fee.transactionsCount, 0);
    const averageFee = totalTransactions > 0 ? totalCollected / totalTransactions : 0;

    const analytics = {
      totalCollected,
      activeFees,
      averageFee: Math.round(averageFee),
      totalTransactions
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching fee analytics:', error);
    res.status(500).json({ error: 'Failed to fetch fee analytics' });
  }
});

// Apply fee to transaction
router.post('/apply', authenticateToken, async (req, res) => {
  try {
    const { userId, transactionType, amount, feeId } = req.body;

    // Find the applicable fee
    const fee = feeStructures.find(f => f.id === feeId && f.isActive);
    if (!fee) {
      return res.status(404).json({ error: 'Fee structure not found or inactive' });
    }

    // Calculate fee amount
    let feeAmount = 0;
    if (fee.feeType === 'fixed') {
      feeAmount = fee.amount;
    } else if (fee.feeType === 'percentage') {
      feeAmount = (amount * fee.percentage) / 100;
    }

    // Apply limits
    if (fee.minimumAmount > 0 && feeAmount < fee.minimumAmount) {
      feeAmount = fee.minimumAmount;
    }
    if (fee.maximumAmount > 0 && feeAmount > fee.maximumAmount) {
      feeAmount = fee.maximumAmount;
    }

    // Create transaction record
    const feeTransaction = new Transaction({
      userId,
      type: 'fee',
      amount: feeAmount,
      description: `${fee.name} for ${transactionType}`,
      status: 'completed'
    });

    await feeTransaction.save();

    // Update fee statistics
    const feeIndex = feeStructures.findIndex(f => f.id === feeId);
    if (feeIndex !== -1) {
      feeStructures[feeIndex].totalCollected += feeAmount;
      feeStructures[feeIndex].transactionsCount += 1;
    }

    res.json({ 
      message: 'Fee applied successfully',
      feeAmount,
      transaction: feeTransaction
    });
  } catch (error) {
    console.error('Error applying fee:', error);
    res.status(500).json({ error: 'Failed to apply fee' });
  }
});

// Get fee categories
router.get('/categories', authenticateToken, async (req, res) => {
  try {
    const categories = [
      { value: 'transaction', label: 'Transaction' },
      { value: 'withdrawal', label: 'Withdrawal' },
      { value: 'transfer', label: 'Transfer' },
      { value: 'maintenance', label: 'Maintenance' },
      { value: 'service', label: 'Service' },
      { value: 'penalty', label: 'Penalty' }
    ];

    res.json(categories);
  } catch (error) {
    console.error('Error fetching fee categories:', error);
    res.status(500).json({ error: 'Failed to fetch fee categories' });
  }
});

module.exports = router; 