const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');

// GET /api/admin/settings/history - Get settings history
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 50, search = '', section = '' } = req.query;
    
    // Mock settings history data - in a real implementation, this would come from a database
    const mockSettingsHistory = [
      {
        _id: '1',
        version: 3,
        section: 'app-config',
        changes: { appName: 'ASUSU by Koja v2' },
        previousValue: { appName: 'ASUSU by Koja' },
        newValue: { appName: 'ASUSU by Koja v2' },
        userId: req.user.id,
        userEmail: req.user.email,
        userName: `${req.user.firstName} ${req.user.lastName}`,
        timestamp: new Date().toISOString(),
        reverted: false
      },
      {
        _id: '2',
        version: 2,
        section: 'colors',
        changes: { primary: '#16A34A', secondary: '#15803D' },
        previousValue: { primary: '#3B82F6', secondary: '#1D4ED8' },
        newValue: { primary: '#16A34A', secondary: '#15803D' },
        userId: req.user.id,
        userEmail: req.user.email,
        userName: `${req.user.firstName} ${req.user.lastName}`,
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        reverted: false
      },
      {
        _id: '3',
        version: 1,
        section: 'api-keys',
        changes: { 'paystack.publicKey': 'pk_test_updated' },
        previousValue: { 'paystack.publicKey': 'pk_test_old' },
        newValue: { 'paystack.publicKey': 'pk_test_updated' },
        userId: req.user.id,
        userEmail: req.user.email,
        userName: `${req.user.firstName} ${req.user.lastName}`,
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        reverted: true,
        revertedBy: '<EMAIL>',
        revertedAt: new Date(Date.now() - 3600000).toISOString()
      }
    ];

    // Filter history based on search and filters
    let filteredHistory = mockSettingsHistory;

    if (search) {
      filteredHistory = filteredHistory.filter(entry =>
        entry.section.toLowerCase().includes(search.toLowerCase()) ||
        entry.userName.toLowerCase().includes(search.toLowerCase()) ||
        entry.userEmail.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (section && section !== 'all') {
      filteredHistory = filteredHistory.filter(entry => entry.section === section);
    }

    // Pagination
    const pageNumber = parseInt(page);
    const limitNumber = parseInt(limit);
    const startIndex = (pageNumber - 1) * limitNumber;
    const endIndex = startIndex + limitNumber;
    const paginatedHistory = filteredHistory.slice(startIndex, endIndex);

    res.json({
      success: true,
      history: paginatedHistory,
      pagination: {
        page: pageNumber,
        limit: limitNumber,
        total: filteredHistory.length,
        pages: Math.ceil(filteredHistory.length / limitNumber)
      }
    });
  } catch (error) {
    console.error('Error fetching settings history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings history'
    });
  }
});

// POST /api/admin/settings/revert - Revert settings to a previous version
router.post('/revert', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { entryId } = req.body;

    if (!entryId) {
      return res.status(400).json({
        success: false,
        message: 'Entry ID is required'
      });
    }

    // Mock revert operation - in a real implementation, this would update the actual settings
    console.log(`Admin ${req.user.email} reverted settings entry ${entryId}`);

    res.json({
      success: true,
      message: 'Settings reverted successfully'
    });
  } catch (error) {
    console.error('Error reverting settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to revert settings'
    });
  }
});

// GET /api/admin/settings/history/export - Export settings history
router.get('/export', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Mock settings history data for export
    const settingsHistory = [
      {
        timestamp: new Date().toISOString(),
        version: 3,
        section: 'app-config',
        user: `${req.user.firstName} ${req.user.lastName}`,
        userEmail: req.user.email,
        changes: 'appName: ASUSU by Koja v2',
        reverted: false
      }
    ];

    // Convert to CSV format
    const csvHeader = 'Timestamp,Version,Section,User,User Email,Changes,Reverted\n';
    const csvRows = settingsHistory.map(entry => 
      `${entry.timestamp},${entry.version},"${entry.section}","${entry.user}","${entry.userEmail}","${entry.changes}",${entry.reverted}`
    ).join('\n');
    
    const csvContent = csvHeader + csvRows;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=settings-history-${new Date().toISOString().split('T')[0]}.csv`);
    res.send(csvContent);
  } catch (error) {
    console.error('Error exporting settings history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export settings history'
    });
  }
});

module.exports = router; 