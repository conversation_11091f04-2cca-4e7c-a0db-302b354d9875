const express = require('express');
const Profit = require('../models/profit');
const SavingsPlan = require('../models/savingsPlan');
const Transaction = require('../models/transaction');
const router = express.Router();

// Calculate and add profits to a user's savings plan
router.post('/calculate', async (req, res) => {
  try {
    const { userId, savingsPlanId, interestRate } = req.body;
    
    // Validate required fields
    if (!userId || !savingsPlanId) {
      return res.status(400).json({ error: 'User and savings plan are required.' });
    }
    if (typeof interestRate !== 'undefined' && (typeof interestRate !== 'number' || interestRate <= 0)) {
      return res.status(400).json({ error: 'Interest rate must be a positive number.' });
    }
    
    // Default interest rate if not provided (3.5% annual)
    const rate = interestRate || 0.035;
    
    // Find the savings plan
    const savingsPlan = await SavingsPlan.findById(savingsPlanId);
    if (!savingsPlan) {
      return res.status(404).json({ error: 'Savings plan not found.' });
    }
    
    // Calculate profit based on current amount and interest rate
    // For simplicity, we're calculating daily interest as (annual rate / 365)
    const dailyRate = rate / 365;
    const profitAmount = savingsPlan.depositAmount * dailyRate;
    
    // Create a new profit record
    const newProfit = new Profit({
      userId,
      savingsPlanId,
      amount: profitAmount,
      interestRate: rate,
      description: `Interest accrued at ${(rate * 100).toFixed(2)}% annual rate`,
      status: 'applied'
    });
    
    const savedProfit = await newProfit.save();
    
    // Create a corresponding transaction for the profit
    const profitTransaction = new Transaction({
      userId,
      type: 'interest',
      amount: profitAmount,
      description: `Interest earned on ${savingsPlan.title}`,
      savingsPlanId,
      status: 'completed'
    });
    
    await profitTransaction.save();
    
    res.status(201).json(savedProfit);
  } catch (error) {
    console.error('Error calculating profit:', error);
    res.status(500).json({ error: 'Failed to calculate profit', details: error.message });
  }
});

// Get all profits for a user
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const profits = await Profit.find({ userId }).sort({ calculationDate: -1 });
    
    // Calculate total profit
    const totalProfit = profits.reduce((sum, profit) => sum + profit.amount, 0);
    
    res.status(200).json({
      profits,
      totalProfit
    });
  } catch (error) {
    console.error('Error fetching profits:', error);
    res.status(500).json({ error: 'Failed to fetch profits', details: error.message });
  }
});

// Get profits for a specific savings plan
router.get('/plan/:savingsPlanId', async (req, res) => {
  try {
    const { savingsPlanId } = req.params;
    const profits = await Profit.find({ savingsPlanId }).sort({ calculationDate: -1 });
    
    // Calculate total profit for this plan
    const totalProfit = profits.reduce((sum, profit) => sum + profit.amount, 0);
    
    res.status(200).json({
      profits,
      totalProfit
    });
  } catch (error) {
    console.error('Error fetching plan profits:', error);
    res.status(500).json({ error: 'Failed to fetch plan profits', details: error.message });
  }
});

// Update a profit status
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }
    
    const updatedProfit = await Profit.findByIdAndUpdate(
      id, 
      { status }, 
      { new: true }
    );
    
    if (!updatedProfit) {
      return res.status(404).json({ error: 'Profit record not found' });
    }
    
    res.status(200).json(updatedProfit);
  } catch (error) {
    console.error('Error updating profit status:', error);
    res.status(500).json({ error: 'Failed to update profit status', details: error.message });
  }
});

module.exports = router;
