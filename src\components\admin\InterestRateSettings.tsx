import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Percent, Clock, DollarSign, Save } from "lucide-react";
import { toast } from "sonner";

interface InterestRate {
  id: string;
  name: string;
  rate: number;
  period: string;
  minAmount: number;
  maxAmount?: number;
  accountTier: string;
  isActive: boolean;
}

export const InterestRateSettings = () => {
  const [rates, setRates] = useState<InterestRate[]>([
    {
      id: "1",
      name: "Basic Savings",
      rate: 5.5,
      period: "annual",
      minAmount: 1000,
      maxAmount: 100000,
      accountTier: "basic",
      isActive: true
    },
    {
      id: "2", 
      name: "Premium Savings",
      rate: 8.0,
      period: "annual",
      minAmount: 50000,
      maxAmount: 1000000,
      accountTier: "premium",
      isActive: true
    }
  ]);

  const [newRate, setNewRate] = useState({
    name: "",
    rate: "",
    period: "annual",
    minAmount: "",
    maxAmount: "",
    accountTier: "basic"
  });

  const handleAddRate = () => {
    if (!newRate.name || !newRate.rate || !newRate.minAmount) {
      toast.error("Please fill in all required fields");
      return;
    }

    const rate: InterestRate = {
      id: Date.now().toString(),
      name: newRate.name,
      rate: parseFloat(newRate.rate),
      period: newRate.period,
      minAmount: parseFloat(newRate.minAmount),
      maxAmount: newRate.maxAmount ? parseFloat(newRate.maxAmount) : undefined,
      accountTier: newRate.accountTier,
      isActive: true
    };

    setRates([...rates, rate]);
    setNewRate({
      name: "",
      rate: "",
      period: "annual",
      minAmount: "",
      maxAmount: "",
      accountTier: "basic"
    });

    toast.success("Interest rate added successfully");
  };

  const toggleRateStatus = (id: string) => {
    setRates(rates.map(rate => 
      rate.id === id ? { ...rate, isActive: !rate.isActive } : rate
    ));
    toast.success("Interest rate status updated");
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="h-5 w-5" />
            Add New Interest Rate
          </CardTitle>
          <CardDescription>
            Configure interest rates for different account tiers and amounts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="rateName">Rate Name</Label>
              <Input
                id="rateName"
                value={newRate.name}
                onChange={(e) => setNewRate(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Premium Savings"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="rate">Interest Rate (%)</Label>
              <Input
                id="rate"
                type="number"
                step="0.1"
                value={newRate.rate}
                onChange={(e) => setNewRate(prev => ({ ...prev, rate: e.target.value }))}
                placeholder="e.g., 8.5"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="period">Period</Label>
              <Select value={newRate.period} onValueChange={(value) => setNewRate(prev => ({ ...prev, period: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="annual">Annual</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accountTier">Account Tier</Label>
              <Select value={newRate.accountTier} onValueChange={(value) => setNewRate(prev => ({ ...prev, accountTier: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="minAmount">Minimum Amount (₦)</Label>
              <Input
                id="minAmount"
                type="number"
                value={newRate.minAmount}
                onChange={(e) => setNewRate(prev => ({ ...prev, minAmount: e.target.value }))}
                placeholder="e.g., 1000"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxAmount">Maximum Amount (₦) - Optional</Label>
              <Input
                id="maxAmount"
                type="number"
                value={newRate.maxAmount}
                onChange={(e) => setNewRate(prev => ({ ...prev, maxAmount: e.target.value }))}
                placeholder="e.g., 100000"
              />
            </div>
          </div>

          <Button onClick={handleAddRate} className="gap-2">
            <Save className="h-4 w-4" />
            Add Interest Rate
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Current Interest Rates</CardTitle>
          <CardDescription>Manage existing interest rate configurations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {rates.map((rate) => (
              <div key={rate.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold">{rate.name}</h4>
                    <Badge variant={rate.isActive ? "default" : "secondary"}>
                      {rate.isActive ? "Active" : "Inactive"}
                    </Badge>
                    <Badge variant="outline">{rate.accountTier}</Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Percent className="h-3 w-3" />
                      {rate.rate}% {rate.period}
                    </span>
                    <span className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      ₦{rate.minAmount.toLocaleString()} - {rate.maxAmount ? `₦${rate.maxAmount.toLocaleString()}` : "No limit"}
                    </span>
                  </div>
                </div>
                <Button
                  variant={rate.isActive ? "destructive" : "default"}
                  size="sm"
                  onClick={() => toggleRateStatus(rate.id)}
                >
                  {rate.isActive ? "Deactivate" : "Activate"}
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};