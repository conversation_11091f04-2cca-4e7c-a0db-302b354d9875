import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Calendar, CreditCard, Repeat } from 'lucide-react';

const AutoSaveSettings = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [amount, setAmount] = useState('');
  const [frequency, setFrequency] = useState('');
  const [selectedCard, setSelectedCard] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const savedCards = [
    { id: '1', last4: '1234', brand: 'Visa', bank: 'GTBank' },
    { id: '2', last4: '5678', brand: 'Mastercard', bank: 'Access Bank' }
  ];

  const handleSaveSettings = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast({
        title: "AutoSave Updated!",
        description: isEnabled 
          ? `AutoSave enabled: ₦${amount} every ${frequency}.`
          : "AutoSave has been disabled.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update AutoSave settings.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
          <Repeat className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">AutoSave Settings</h2>
          <p className="text-sm text-muted-foreground">Automate your savings habit</p>
        </div>
      </div>

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">Enable AutoSave</h3>
            <p className="text-sm text-muted-foreground">
              Automatically save at regular intervals
            </p>
          </div>
          <Switch
            checked={isEnabled}
            onCheckedChange={setIsEnabled}
          />
        </div>

        {isEnabled && (
          <div className="space-y-4 animate-fade-in">
            <FloatingLabelInput
              id="amount"
              type="number"
              label="Amount per save (₦)"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="1,000"
              min="100"
            />

            <div className="space-y-2">
              <label className="text-sm font-medium">Frequency</label>
              <Select value={frequency} onValueChange={setFrequency}>
                <SelectTrigger>
                  <SelectValue placeholder="How often?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Payment Card</label>
              <Select value={selectedCard} onValueChange={setSelectedCard}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a card" />
                </SelectTrigger>
                <SelectContent>
                  {savedCards.map((card) => (
                    <SelectItem key={card.id} value={card.id}>
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4" />
                        <span>{card.brand} **** {card.last4} ({card.bank})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {amount && frequency && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">AutoSave Summary</span>
                </div>
                <p className="text-sm text-blue-600">
                  ₦{parseFloat(amount).toLocaleString()} will be saved {frequency} from your selected card.
                </p>
                <p className="text-xs text-blue-600/80 mt-1">
                  Monthly total: ₦{(parseFloat(amount) * (frequency === 'daily' ? 30 : frequency === 'weekly' ? 4 : 1)).toLocaleString()}
                </p>
              </div>
            )}
          </div>
        )}

        <Button 
          onClick={handleSaveSettings}
          className="w-full"
          disabled={loading || (isEnabled && (!amount || !frequency || !selectedCard))}
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <span className="animate-spin mr-2">⟳</span> Saving...
            </span>
          ) : (
            'Save Settings'
          )}
        </Button>
      </div>
    </FintechCard>
  );
};

export default AutoSaveSettings;