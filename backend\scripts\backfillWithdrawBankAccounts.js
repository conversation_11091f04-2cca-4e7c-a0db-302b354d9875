// Script to backfill bankAccountId for old Withdraw records
// Usage: node backend/scripts/backfillWithdrawBankAccounts.js

const mongoose = require('mongoose');
const Withdraw = require('../models/withdraw');
const WithdrawAccount = require('../models/withdrawAccounts');
const User = require('../models/user');

const MONGO_URI = 'mongodb+srv://obibiifeanyi:<EMAIL>/?retryWrites=true&w=majority'; // Update as needed

async function main() {
  await mongoose.connect(MONGO_URI);
  console.log('Connected to MongoDB');

  // Find all withdrawals missing bankAccountId
  const withdrawals = await Withdraw.find({ $or: [ { bankAccountId: { $exists: false } }, { bankAccountId: null } ] });
  console.log(`Found ${withdrawals.length} withdrawals to update.`);

  for (const withdrawal of withdrawals) {
    // Find a default or first withdraw account for the user
    const account = await WithdrawAccount.findOne({ userId: withdrawal.userId });
    if (account) {
      withdrawal.bankAccountId = account._id;
      await withdrawal.save();
      console.log(`Updated withdrawal ${withdrawal._id} with bankAccountId ${account._id}`);
    } else {
      console.log(`No withdraw account found for user ${withdrawal.userId} (withdrawal ${withdrawal._id})`);
    }
  }

  await mongoose.disconnect();
  console.log('Done.');
}

main().catch(err => {
  console.error(err);
  process.exit(1);
});
