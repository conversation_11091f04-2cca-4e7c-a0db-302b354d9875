// Script to backfill old withdrawals into the Transaction model
const mongoose = require('mongoose');
const Withdraw = require('../models/withdraw');
const Transaction = require('../models/transaction');
const User = require('../models/user');

const MONGO_URI = 'mongodb+srv://kojaonlineltd:<EMAIL>/?retryWrites=true&w=majority&appName=asusuamac'; // Change if needed

// Helper to generate a 6-character alphanumeric reference
function generateReference() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let ref = '';
  for (let i = 0; i < 6; i++) {
    ref += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return ref;
}

async function backfillWithdrawals() {
  await mongoose.connect(MONGO_URI);
  // You can filter by status if needed, e.g. { status: 'approved' }
  const withdrawals = await Withdraw.find();
  let created = 0, skipped = 0;
  for (const withdrawal of withdrawals) {
    // Check if transaction already exists for this withdrawal (by user, amount, and date)
    const exists = await Transaction.findOne({
      userId: withdrawal.userId,
      amount: withdrawal.amount,
      type: 'withdrawal',
      date: withdrawal.createdAt,
    });
    if (exists) {
      skipped++;
      continue;
    }
    // Get user's balance after this withdrawal (best effort, may not be exact for old data)
    const user = await User.findById(withdrawal.userId);
    // Generate unique reference
    let reference;
    let refExists = true;
    while (refExists) {
      reference = generateReference();
      refExists = await Transaction.exists({ reference });
    }
    await Transaction.create({
      userId: withdrawal.userId,
      type: 'withdrawal',
      amount: withdrawal.amount,
      description: withdrawal.notes || 'Withdrawal',
      date: withdrawal.createdAt,
      balanceAfter: user ? user.balance : 0,
      reference,
    });
    created++;
  }
  console.log(`Backfill complete. Created: ${created}, Skipped (already exists): ${skipped}`);
  await mongoose.disconnect();
}

backfillWithdrawals().catch(err => {
  console.error('Backfill error:', err);
  process.exit(1);
});
