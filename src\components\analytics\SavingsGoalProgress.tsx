
import React from "react";
import { Progress } from "@/components/ui/progress";

interface SavingsGoalProgressProps {
  currentAmount: number;
  goalAmount: number;
}

export function SavingsGoalProgress({ currentAmount, goalAmount }: SavingsGoalProgressProps) {
  // Calculate progress percentage
  const progressPercentage = Math.min(
    Math.round((currentAmount / goalAmount) * 100),
    100
  );

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span className="font-medium">
          ₦{currentAmount.toLocaleString()}
        </span>
        <span className="text-muted-foreground">
          Goal: ₦{goalAmount.toLocaleString()}
        </span>
      </div>
      
      <Progress 
        value={progressPercentage} 
        className="h-2.5"
      />
      
      <div className="text-xs text-muted-foreground text-center">
        {progressPercentage}% Complete
      </div>
    </div>
  );
}
