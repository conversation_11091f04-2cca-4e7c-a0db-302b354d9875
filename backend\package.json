{"name": "backend", "version": "1.0.0", "description": "", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js", "build": "echo 'Backend build complete'", "start": "node index.js", "postinstall": "prisma generate"}, "dependencies": {"prisma": "^6.12.0", "@aws-sdk/client-s3": "^3.817.0", "@prisma/client": "^6.12.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.2", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "node-cron": "^4.0.7", "nodemailer": "^7.0.4"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.10", "nodemon": "^3.1.10"}}