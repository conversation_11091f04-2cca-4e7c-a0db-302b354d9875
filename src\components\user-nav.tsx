
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { NotificationCenter } from "@/components/notification/notification-center"
import { Link } from "react-router-dom"
import { HelpCircle, User, Settings, LogOut } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"

export function UserNav({ isAdmin = false }) {
  const { user, signOut } = useAuth();
  const logoutPath = isAdmin ? "/admin/login" : "/login";
  const dashboardPath = isAdmin ? "/admin/dashboard" : "/dashboard";
  
  // Get initials for avatar fallback
  const getInitials = () => {
    if (!user || !user.profile) return isAdmin ? "AD" : "US";
    
    const firstName = user.profile.first_name || "";
    const lastName = user.profile.last_name || "";
    
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };
  
  // Get full name
  const getFullName = () => {
    if (!user || !user.profile) return isAdmin ? "ADMIN USER" : "USER";
    
    return `${user.profile.first_name} ${user.profile.last_name}`;
  };

  return (
    <div className="flex items-center gap-4">
      <Button 
        variant="ghost" 
        size="icon" 
        asChild
        className="relative hover:bg-brand-yellow/20"
        title="Help Center"
      >
        <Link to="/help">
          <HelpCircle className="h-5 w-5 text-brand-yellow" />
        </Link>
      </Button>

      <NotificationCenter />
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <Avatar className="h-9 w-9 transition-all duration-300 ring-2 ring-brand-yellow/30 hover:ring-brand-yellow">
              <AvatarImage 
                src={user?.profile?.avatar_url || "/avatar.jpg"} 
                alt="Avatar" 
              />
              <AvatarFallback className="bg-brand-blue text-white font-unica-one">
                {getInitials()}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-unica-one tracking-wide leading-none">
                {getFullName()}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {user?.email || "<EMAIL>"}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem asChild>
              <Link to={dashboardPath} className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Dashboard
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link to="/settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            className="flex items-center gap-2 text-red-500 cursor-pointer"
            onClick={signOut}
          >
            <LogOut className="h-4 w-4" />
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
