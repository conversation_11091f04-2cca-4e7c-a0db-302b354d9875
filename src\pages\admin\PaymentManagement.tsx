
import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { WithdrawalApprovalTable } from "@/components/payments/WithdrawalApprovalTable";
import { WithdrawalDetailsModal } from "@/components/payments/WithdrawalDetailsModal";
import { useToast } from "@/hooks/use-toast";
import { 
  CreditCard, 
  Plus, 
  Pencil, 
  Trash2, 
  FileText,
  Wallet,
  ArrowUpRight,
  ArrowDownLeft
} from "lucide-react";

interface PaymentMethod {
  id: string;
  name: string;
  accountNumber?: string;
  bankName?: string;
  isActive: boolean;
  type: "bank" | "card" | "ussd" | "qr";
}

interface WithdrawalRequest {
  id: string;
  userId: string;
  userName: string;
  amount: number;
  date: string;
  status: "pending" | "approved" | "rejected";
  bankName: string;
  accountNumber: string;
  accountName: string;
  reason?: string;
}

export default function PaymentManagement() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("methods");
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: "1",
      name: "Bank Transfer - GTBank",
      accountNumber: "**********",
      bankName: "Guaranty Trust Bank",
      isActive: true,
      type: "bank",
    },
    {
      id: "2",
      name: "Mastercard/Visa",
      isActive: true,
      type: "card",
    },
    {
      id: "3",
      name: "USSD Payment",
      isActive: false,
      type: "ussd",
    },
  ]);

  const [withdrawalRequests, setWithdrawalRequests] = useState<WithdrawalRequest[]>([
    {
      id: "w1",
      userId: "u1",
      userName: "Chioma Okafor",
      amount: 25000,
      date: "2023-09-15",
      status: "pending",
      bankName: "First Bank",
      accountNumber: "**********",
      accountName: "Chioma Okafor",
      reason: "Need funds for school fees payment",
    },
    {
      id: "w2",
      userId: "u2",
      userName: "Emeka Adekunle",
      amount: 50000,
      date: "2023-09-14",
      status: "approved",
      bankName: "Access Bank",
      accountNumber: "**********",
      accountName: "Emeka Adekunle",
    },
    {
      id: "w3",
      userId: "u3",
      userName: "Fatima Ibrahim",
      amount: 15000,
      date: "2023-09-10",
      status: "rejected",
      bankName: "UBA",
      accountNumber: "**********",
      accountName: "Fatima Ibrahim",
      reason: "Emergency medical expenses",
    },
  ]);

  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRequest | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [newMethod, setNewMethod] = useState<Partial<PaymentMethod>>({
    name: "",
    accountNumber: "",
    bankName: "",
    type: "bank",
    isActive: true,
  });

  const handleApproveWithdrawal = (id: string) => {
    setWithdrawalRequests(prev => 
      prev.map(req => req.id === id ? {...req, status: "approved"} : req)
    );
    
    toast({
      title: "Withdrawal Approved",
      description: "The withdrawal request has been approved successfully.",
    });
  };

  const handleRejectWithdrawal = (id: string) => {
    setWithdrawalRequests(prev => 
      prev.map(req => req.id === id ? {...req, status: "rejected"} : req)
    );
    
    toast({
      title: "Withdrawal Rejected",
      description: "The withdrawal request has been rejected.",
    });
  };

  const handleViewDetails = (request: WithdrawalRequest) => {
    setSelectedWithdrawal(request);
    setIsDetailsModalOpen(true);
  };

  const handleAddMethod = () => {
    if (!newMethod.name) {
      toast({
        title: "Error",
        description: "Payment method name is required",
        variant: "destructive",
      });
      return;
    }

    if (isEditing && newMethod.id) {
      setPaymentMethods(prev => 
        prev.map(method => method.id === newMethod.id 
          ? { ...method, ...newMethod } as PaymentMethod 
          : method
        )
      );
      
      toast({
        title: "Method Updated",
        description: "Payment method has been updated successfully",
      });
    } else {
      const id = `method-${Date.now()}`;
      setPaymentMethods(prev => [...prev, { ...newMethod, id, isActive: true } as PaymentMethod]);
      
      toast({
        title: "Method Added",
        description: "New payment method has been added successfully",
      });
    }
    
    setNewMethod({
      name: "",
      accountNumber: "",
      bankName: "",
      type: "bank",
      isActive: true,
    });
    setIsEditing(false);
  };

  const handleEditMethod = (method: PaymentMethod) => {
    setNewMethod(method);
    setIsEditing(true);
  };

  const handleDeleteMethod = (id: string) => {
    setPaymentMethods(prev => prev.filter(method => method.id !== id));
    toast({
      title: "Method Deleted",
      description: "Payment method has been deleted successfully",
    });
  };

  const handleToggleMethodStatus = (id: string) => {
    setPaymentMethods(prev => 
      prev.map(method => method.id === id 
        ? { ...method, isActive: !method.isActive } 
        : method
      )
    );
    
    const method = paymentMethods.find(m => m.id === id);
    toast({
      title: method?.isActive ? "Method Disabled" : "Method Enabled",
      description: `Payment method has been ${method?.isActive ? "disabled" : "enabled"} successfully`,
    });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Payment Management</h2>
        <p className="text-muted-foreground">
          Manage payment methods and withdrawal requests
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="methods">
            <CreditCard className="mr-2 h-4 w-4" />
            Payment Methods
          </TabsTrigger>
          <TabsTrigger value="withdrawals">
            <FileText className="mr-2 h-4 w-4" />
            Withdrawal Requests
          </TabsTrigger>
        </TabsList>

        <TabsContent value="methods" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Add Payment Method</CardTitle>
              <CardDescription>
                Add or update payment methods that users can use to fund their accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="method-name">Method Name</Label>
                  <Input
                    id="method-name"
                    placeholder="e.g. Bank Transfer - GTBank"
                    value={newMethod.name || ""}
                    onChange={(e) => setNewMethod({ ...newMethod, name: e.target.value })}
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="method-type">Payment Type</Label>
                  <select
                    id="method-type"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={newMethod.type || "bank"}
                    onChange={(e) => setNewMethod({ ...newMethod, type: e.target.value as any })}
                  >
                    <option value="bank">Bank Transfer</option>
                    <option value="card">Card Payment</option>
                    <option value="ussd">USSD</option>
                    <option value="qr">QR Code</option>
                  </select>
                </div>
                
                {newMethod.type === "bank" && (
                  <>
                    <div className="grid gap-2">
                      <Label htmlFor="bank-name">Bank Name</Label>
                      <Input
                        id="bank-name"
                        placeholder="e.g. Guaranty Trust Bank"
                        value={newMethod.bankName || ""}
                        onChange={(e) => setNewMethod({ ...newMethod, bankName: e.target.value })}
                      />
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="account-number">Account Number</Label>
                      <Input
                        id="account-number"
                        placeholder="e.g. **********"
                        value={newMethod.accountNumber || ""}
                        onChange={(e) => setNewMethod({ ...newMethod, accountNumber: e.target.value })}
                      />
                    </div>
                  </>
                )}
                
                <Button onClick={handleAddMethod}>
                  {isEditing ? "Update Method" : "Add Method"}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Active Payment Methods</CardTitle>
              <CardDescription>
                Available payment methods for users to fund their accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentMethods.length === 0 ? (
                  <div className="text-center py-4 text-muted-foreground">
                    No payment methods available. Add one above.
                  </div>
                ) : (
                  paymentMethods.map((method) => (
                    <div
                      key={method.id}
                      className={`flex items-center justify-between p-4 rounded-lg border ${
                        method.isActive
                          ? "bg-accent/40 border-primary/20"
                          : "bg-muted/40 border-muted"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        {method.type === "bank" && <Building2 className="h-5 w-5 text-primary" />}
                        {method.type === "card" && <CreditCard className="h-5 w-5 text-primary" />}
                        {method.type === "ussd" && <FileText className="h-5 w-5 text-primary" />}
                        {method.type === "qr" && <QrCode className="h-5 w-5 text-primary" />}
                        <div>
                          <div className="font-medium">{method.name}</div>
                          {method.type === "bank" && (
                            <div className="text-sm text-muted-foreground">
                              {method.bankName} - {method.accountNumber}
                            </div>
                          )}
                          <div className="text-xs mt-1">
                            <span
                              className={`px-2 py-0.5 rounded-full ${
                                method.isActive
                                  ? "bg-green-100 text-green-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {method.isActive ? "Active" : "Inactive"}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleMethodStatus(method.id)}
                        >
                          {method.isActive ? "Disable" : "Enable"}
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleEditMethod(method)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="text-destructive"
                          onClick={() => handleDeleteMethod(method.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="withdrawals" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <ArrowUpRight className="mr-2 h-4 w-4 text-destructive" />
                  Pending Withdrawals
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {withdrawalRequests.filter(r => r.status === "pending").length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Withdrawal requests awaiting approval
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <ArrowDownLeft className="mr-2 h-4 w-4 text-green-500" />
                  Approved This Month
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ₦{withdrawalRequests
                    .filter(r => r.status === "approved")
                    .reduce((sum, r) => sum + r.amount, 0)
                    .toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total approved withdrawals
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <Wallet className="mr-2 h-4 w-4 text-brand-blue" />
                  Total Processed
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {withdrawalRequests.filter(r => r.status !== "pending").length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total processed withdrawal requests
                </p>
              </CardContent>
            </Card>
          </div>
          
          <WithdrawalApprovalTable
            withdrawalRequests={withdrawalRequests}
            onApprove={handleApproveWithdrawal}
            onReject={handleRejectWithdrawal}
            onViewDetails={handleViewDetails}
          />
          
          <WithdrawalDetailsModal
            isOpen={isDetailsModalOpen}
            onClose={() => setIsDetailsModalOpen(false)}
            request={selectedWithdrawal}
            onApprove={handleApproveWithdrawal}
            onReject={handleRejectWithdrawal}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Import missing icons
import { Building2, QrCode } from "lucide-react";
