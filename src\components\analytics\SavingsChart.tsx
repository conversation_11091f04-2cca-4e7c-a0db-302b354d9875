import { FintechCard } from '@/components/ui/fintech-card';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';

const savingsData = [
  { month: 'Jan', amount: 45000, target: 50000 },
  { month: 'Feb', amount: 52000, target: 60000 },
  { month: 'Mar', amount: 61000, target: 70000 },
  { month: 'Apr', amount: 74000, target: 80000 },
  { month: 'May', amount: 89000, target: 90000 },
  { month: 'Jun', amount: 95000, target: 100000 },
  { month: 'Jul', amount: 108000, target: 110000 },
  { month: 'Aug', amount: 125000, target: 120000 },
];

export const SavingsChart = () => {
  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold">Savings Growth</h3>
          <p className="text-sm text-muted-foreground">Monthly savings vs targets</p>
        </div>
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-primary"></div>
            <span>Actual</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-primary/40"></div>
            <span>Target</span>
          </div>
        </div>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={savingsData}>
            <defs>
              <linearGradient id="savingsGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
            <XAxis 
              dataKey="month" 
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
            />
            <YAxis 
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickFormatter={(value) => `₦${(value / 1000)}k`}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'hsl(var(--card))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '8px',
                color: 'hsl(var(--card-foreground))'
              }}
              formatter={(value) => [`₦${Number(value).toLocaleString()}`, '']}
            />
            <Area
              type="monotone"
              dataKey="amount"
              stroke="hsl(var(--primary))"
              strokeWidth={2}
              fill="url(#savingsGradient)"
            />
            <Line
              type="monotone"
              dataKey="target"
              stroke="hsl(var(--primary))"
              strokeWidth={1}
              strokeDasharray="5 5"
              dot={false}
              opacity={0.7}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </FintechCard>
  );
};