const mongoose = require('mongoose');
const KYC = require('../models/kyc');
const User = require('../models/user');

// Connect to the database
mongoose.connect('mongodb+srv://obibiifeanyi:<EMAIL>/?retryWrites=true&w=majority', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const db = mongoose.connection;
db.on('error', console.error.bind(console, 'connection error:'));
db.once('open', async () => {
  console.log('Connected to the database');

  try {
    // Fetch all KYC records without a userId
    const kycsWithoutUserId = await KYC.find({ userId: { $exists: false } });

    for (const kyc of kycsWithoutUserId) {
      // Find a user to associate with the KYC record (example logic, adjust as needed)
      const user = await User.findOne({ email: kyc.email }); // Assuming email is a common field

      if (user) {
        kyc.userId = user._id;
        await kyc.save();
        console.log(`Updated KYC record with ID: ${kyc._id}`);
      } else {
        console.warn(`No user found for KYC record with ID: ${kyc._id}`);
      }
    }

    console.log('Migration completed');
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    mongoose.connection.close();
  }
});
