
import { DollarSign, <PERSON>Up, <PERSON>Down, TrendingUp } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface RevenueCardProps {
  title: string;
  value: string;
  period: string;
  trend: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export function RevenueCard({
  title,
  value,
  period,
  trend,
  className,
}: RevenueCardProps) {
  return (
    <Card className={cn(
      "overflow-hidden group transform transition-all duration-500 hover:-translate-y-2 border-green-500/20 hover:border-green-500",
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-green-600 transition-colors duration-300">
          {title}
        </CardTitle>
        <div className="h-8 w-8 rounded-md bg-green-500/10 p-1.5 text-green-600 group-hover:bg-green-500/20 group-hover:text-green-700 transition-all duration-300 transform group-hover:rotate-[-10deg] group-hover:scale-110">
          <DollarSign className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold transition-all duration-500 group-hover:text-green-600 group-hover:translate-x-1">
          {value}
        </div>
        <div className="text-xs text-muted-foreground mt-1 group-hover:text-muted-foreground/80 transition-colors duration-300">
          {period}
        </div>
        {trend && (
          <div className={cn(
            "mt-2 flex items-center text-xs font-medium transition-all duration-300 transform group-hover:translate-x-1",
            trend.isPositive ? "text-green-500" : "text-red-500"
          )}>
            <div className="flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              {trend.isPositive ? (
                <ArrowUp className="h-3 w-3 mr-1" />
              ) : (
                <ArrowDown className="h-3 w-3 mr-1" />
              )}
              <span>{Math.abs(trend.value)}%</span>
            </div>
            <span className="ml-1 text-muted-foreground transition-colors duration-300 group-hover:text-muted-foreground/80">
              from last period
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
