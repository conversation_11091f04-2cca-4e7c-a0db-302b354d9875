
import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./card"
import { cn } from "@/lib/utils"

interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  value: string | number
  description?: string
  icon?: React.ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  className?: string
}

export function StatCard({
  title,
  value,
  description,
  icon,
  trend,
  className,
  ...props
}: StatCardProps) {
  return (
    <Card className={cn("overflow-hidden group transform transition-all duration-500 hover:-translate-y-2", className)} {...props}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-brand-blue transition-colors duration-300">
          {title}
        </CardTitle>
        {icon && (
          <div className="h-8 w-8 rounded-md bg-primary/10 p-1.5 text-primary group-hover:bg-brand-yellow/20 group-hover:text-brand-blue transition-all duration-300 transform group-hover:rotate-[-10deg] group-hover:scale-110">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold transition-all duration-500 group-hover:text-brand-blue group-hover:translate-x-1">
          {value}
        </div>
        {description && <CardDescription className="group-hover:text-muted-foreground/90 transition-colors duration-300">{description}</CardDescription>}
        {trend && (
          <div className={cn(
            "mt-2 flex items-center text-xs font-medium transition-all duration-300 transform group-hover:translate-x-1",
            trend.isPositive ? "text-green-500" : "text-red-500"
          )}>
            <span className="transition-transform duration-300 group-hover:scale-125">{trend.isPositive ? "↑" : "↓"}</span>
            <span className="ml-1">{Math.abs(trend.value)}%</span>
            <span className="ml-1 text-muted-foreground transition-colors duration-300 group-hover:text-muted-foreground/80">from last period</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
