import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, DollarSign, Save, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface PenaltyFee {
  id: string;
  name: string;
  type: "fixed" | "percentage";
  amount: number;
  trigger: string;
  description: string;
  isActive: boolean;
}

export const PenaltySettings = () => {
  const [penalties, setPenalties] = useState<PenaltyFee[]>([
    {
      id: "1",
      name: "Early Withdrawal Fee",
      type: "percentage",
      amount: 2.5,
      trigger: "early_withdrawal",
      description: "Applied when withdrawing before maturity date",
      isActive: true
    },
    {
      id: "2",
      name: "Overdraft Fee",
      type: "fixed",
      amount: 500,
      trigger: "overdraft",
      description: "Fixed fee for insufficient balance transactions",
      isActive: true
    }
  ]);

  const [newPenalty, setNewPenalty] = useState({
    name: "",
    type: "fixed" as "fixed" | "percentage",
    amount: "",
    trigger: "",
    description: ""
  });

  const handleAddPenalty = () => {
    if (!newPenalty.name || !newPenalty.amount || !newPenalty.trigger) {
      toast.error("Please fill in all required fields");
      return;
    }

    const penalty: PenaltyFee = {
      id: Date.now().toString(),
      name: newPenalty.name,
      type: newPenalty.type,
      amount: parseFloat(newPenalty.amount),
      trigger: newPenalty.trigger,
      description: newPenalty.description,
      isActive: true
    };

    setPenalties([...penalties, penalty]);
    setNewPenalty({
      name: "",
      type: "fixed",
      amount: "",
      trigger: "",
      description: ""
    });

    toast.success("Penalty fee added successfully");
  };

  const togglePenaltyStatus = (id: string) => {
    setPenalties(penalties.map(penalty => 
      penalty.id === id ? { ...penalty, isActive: !penalty.isActive } : penalty
    ));
    toast.success("Penalty status updated");
  };

  const deletePenalty = (id: string) => {
    setPenalties(penalties.filter(penalty => penalty.id !== id));
    toast.success("Penalty fee deleted");
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Add New Penalty Fee
          </CardTitle>
          <CardDescription>
            Configure penalty fees for various account violations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="penaltyName">Penalty Name</Label>
              <Input
                id="penaltyName"
                value={newPenalty.name}
                onChange={(e) => setNewPenalty(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Early Withdrawal Fee"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="penaltyType">Fee Type</Label>
              <Select value={newPenalty.type} onValueChange={(value: "fixed" | "percentage") => setNewPenalty(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fixed">Fixed Amount (₦)</SelectItem>
                  <SelectItem value="percentage">Percentage (%)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">
                {newPenalty.type === "fixed" ? "Amount (₦)" : "Percentage (%)"}
              </Label>
              <Input
                id="amount"
                type="number"
                step={newPenalty.type === "percentage" ? "0.1" : "1"}
                value={newPenalty.amount}
                onChange={(e) => setNewPenalty(prev => ({ ...prev, amount: e.target.value }))}
                placeholder={newPenalty.type === "fixed" ? "e.g., 500" : "e.g., 2.5"}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="trigger">Trigger Event</Label>
              <Select value={newPenalty.trigger} onValueChange={(value) => setNewPenalty(prev => ({ ...prev, trigger: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select trigger event" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="early_withdrawal">Early Withdrawal</SelectItem>
                  <SelectItem value="overdraft">Overdraft</SelectItem>
                  <SelectItem value="missed_payment">Missed Payment</SelectItem>
                  <SelectItem value="account_closure">Early Account Closure</SelectItem>
                  <SelectItem value="insufficient_balance">Insufficient Balance</SelectItem>
                  <SelectItem value="failed_transaction">Failed Transaction</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-2 space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={newPenalty.description}
                onChange={(e) => setNewPenalty(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of when this penalty applies"
              />
            </div>
          </div>

          <Button onClick={handleAddPenalty} className="gap-2">
            <Save className="h-4 w-4" />
            Add Penalty Fee
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Current Penalty Fees</CardTitle>
          <CardDescription>Manage existing penalty fee configurations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {penalties.map((penalty) => (
              <div key={penalty.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold">{penalty.name}</h4>
                    <Badge variant={penalty.isActive ? "default" : "secondary"}>
                      {penalty.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      {penalty.type === "fixed" ? `₦${penalty.amount}` : `${penalty.amount}%`}
                    </span>
                    <span>Trigger: {penalty.trigger.replace('_', ' ')}</span>
                  </div>
                  {penalty.description && (
                    <p className="text-sm text-muted-foreground">{penalty.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={penalty.isActive}
                    onCheckedChange={() => togglePenaltyStatus(penalty.id)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deletePenalty(penalty.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};