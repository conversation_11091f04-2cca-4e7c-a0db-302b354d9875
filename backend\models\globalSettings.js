const mongoose = require('mongoose');

// Global Settings Schema for MongoDB
const GlobalSettingsSchema = new mongoose.Schema({
  // App Configuration
  appName: {
    type: String,
    default: 'Better Interest',
    required: true,
    trim: true
  },
  appDescription: {
    type: String,
    default: 'Smart savings platform for Nigerians',
    trim: true
  },
  appVersion: {
    type: String,
    default: '1.0.0',
    trim: true
  },
  
  // Branding & Theme
  logo: {
    url: {
      type: String,
      default: null
    },
    filename: {
      type: String,
      default: null
    },
    uploadedAt: {
      type: Date,
      default: null
    }
  },
  favicon: {
    url: {
      type: String,
      default: null
    },
    filename: {
      type: String,
      default: null
    }
  },
  
  // Color Theme Configuration
  colors: {
    primary: {
      type: String,
      default: '#16A34A', // Green
      validate: {
        validator: function(v) {
          return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
        },
        message: 'Primary color must be a valid hex color'
      }
    },
    secondary: {
      type: String,
      default: '#15803D', // Dark Green
      validate: {
        validator: function(v) {
          return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
        },
        message: 'Secondary color must be a valid hex color'
      }
    },
    accent: {
      type: String,
      default: '#10B981', // Emerald
      validate: {
        validator: function(v) {
          return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
        },
        message: 'Accent color must be a valid hex color'
      }
    },
    background: {
      type: String,
      default: '#FFFFFF',
      validate: {
        validator: function(v) {
          return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
        },
        message: 'Background color must be a valid hex color'
      }
    }
  },
  
  // API Keys & Integrations
  apiKeys: {
    paystack: {
      publicKey: {
        type: String,
        default: null,
        select: false // Hide from regular queries for security
      },
      secretKey: {
        type: String,
        default: null,
        select: false // Hide from regular queries for security
      },
      isLive: {
        type: Boolean,
        default: false
      }
    },
    aws: {
      accessKeyId: {
        type: String,
        default: null,
        select: false
      },
      secretAccessKey: {
        type: String,
        default: null,
        select: false
      },
      region: {
        type: String,
        default: 'eu-north-1'
      },
      s3Bucket: {
        type: String,
        default: null
      }
    }
  },
  
  // Frontend Content Management
  content: {
    landingPage: {
      heroTitle: {
        type: String,
        default: 'Save Smarter with ASUSU'
      },
      heroSubtitle: {
        type: String,
        default: 'Traditional Nigerian savings meets modern technology'
      },
      heroImage: {
        url: String,
        filename: String
      },
      features: [{
        title: String,
        description: String,
        icon: String,
        image: {
          url: String,
          filename: String
        }
      }],
      testimonials: [{
        name: String,
        role: String,
        content: String,
        avatar: {
          url: String,
          filename: String
        },
        rating: {
          type: Number,
          min: 1,
          max: 5,
          default: 5
        }
      }]
    },
    aboutPage: {
      title: {
        type: String,
        default: 'About ASUSU'
      },
      content: {
        type: String,
        default: 'ASUSU brings traditional Nigerian savings into the digital age.'
      },
      images: [{
        url: String,
        filename: String,
        caption: String
      }]
    }
  },
  
  // System Configuration
  system: {
    maintenanceMode: {
      type: Boolean,
      default: false
    },
    maintenanceMessage: {
      type: String,
      default: 'System is under maintenance. Please check back later.'
    },
    allowRegistration: {
      type: Boolean,
      default: true
    },
    emailVerificationRequired: {
      type: Boolean,
      default: true
    },
    kycRequired: {
      type: Boolean,
      default: true
    },
    minimumDeposit: {
      type: Number,
      default: 500,
      min: 0
    },
    maximumDeposit: {
      type: Number,
      default: 1000000,
      min: 0
    }
  },
  
  // Contact Information
  contact: {
    email: {
      type: String,
      default: '<EMAIL>'
    },
    phone: {
      type: String,
      default: '+234-XXX-XXX-XXXX'
    },
    address: {
      type: String,
      default: 'Lagos, Nigeria'
    },
    socialMedia: {
      facebook: String,
      twitter: String,
      instagram: String,
      linkedin: String
    }
  },
  
  // Metadata
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  version: {
    type: Number,
    default: 1
  }
}, { 
  timestamps: true,
  // Ensure only one settings document exists
  collection: 'globalSettings'
});

// Ensure only one settings document can exist
GlobalSettingsSchema.index({}, { unique: true });

// Pre-save middleware to increment version
GlobalSettingsSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.version += 1;
  }
  next();
});

// Static method to get or create settings
// Optionally pass userId to set as lastUpdatedBy if creating for the first time
GlobalSettingsSchema.statics.getSettings = async function(userId = null) {
  let settings = await this.findOne();
  if (!settings) {
    // Use provided userId (admin) if available, else fallback to dummy
    const fallbackUserId = userId || ((typeof mongoose.Types.ObjectId === 'function')
      ? new mongoose.Types.ObjectId()
      : '000000000000000000000000');
    settings = new this({
      lastUpdatedBy: fallbackUserId
    });
    await settings.save();
  }
  return settings;
};

// Static method to update settings
GlobalSettingsSchema.statics.updateSettings = async function(updates, userId) {
  // Pass userId to getSettings so first doc uses admin's id
  const settings = await this.getSettings(userId);
  Object.assign(settings, updates);
  settings.lastUpdatedBy = userId;
  return await settings.save();
};

module.exports = mongoose.model('GlobalSettings', GlobalSettingsSchema);
