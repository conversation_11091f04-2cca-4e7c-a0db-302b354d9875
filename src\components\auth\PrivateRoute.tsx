
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth';
import { LoadingScreen } from '@/components/ui/loading-screen';
import { toast } from 'sonner';

type PrivateRouteProps = {
  requireAdmin?: boolean;
};

export const PrivateRoute = ({ requireAdmin = false }: PrivateRouteProps) => {
  const { user, isAdmin, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!user) {
    // Redirect to login but save the location they were trying to access
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check for blocked or suspended status
  if (user.profile?.status === 'blocked' || user.profile?.status === 'suspended') {
    return <Navigate to="/account-suspended" replace />;
  }

  if (requireAdmin && !isAdmin) {
    // If admin route but user is not admin, redirect to dashboard
    toast.error('You do not have permission to access this area');
    return <Navigate to="/dashboard" replace />;
  }

  // If user is logged in (and is admin if required), allow access to the route
  return <Outlet />;
};
