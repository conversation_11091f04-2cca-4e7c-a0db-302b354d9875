import React from "react";
import { FintechCard } from "@/components/ui/fintech-card";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, Plus, Send, ArrowDownLeft } from "lucide-react";

interface WalletOverviewProps {
  balance: number;
  currency?: string;
  showBalance?: boolean;
  onToggleBalance?: () => void;
  onAddFunds?: () => void;
  onSendMoney?: () => void;
  onWithdraw?: () => void;
}

export function WalletOverview({
  balance,
  currency = "₦",
  showBalance = true,
  onToggleBalance,
  onAddFunds,
  onSendMoney,
  onWithdraw,
}: WalletOverviewProps) {
  const formatBalance = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <FintechCard variant="gradient" className="p-6 text-white bg-gradient-to-br from-primary to-primary/80">
      <div className="flex justify-between items-start mb-6">
        <div>
          <p className="text-white/80 text-sm mb-1">Total Balance</p>
          <div className="flex items-center gap-3">
            <h2 className="text-3xl font-bold">
              {showBalance ? `${currency}${formatBalance(balance)}` : "••••••"}
            </h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleBalance}
              className="text-white hover:bg-white/20 h-8 w-8"
            >
              {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        <div className="bg-white/20 rounded-lg p-2">
          <div className="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center">
            <span className="text-xs font-bold">KP</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-3">
        <Button
          onClick={onAddFunds}
          className="bg-white/20 hover:bg-white/30 text-white border-0 h-12 flex-col gap-1"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
          <span className="text-xs">Add Funds</span>
        </Button>
        
        <Button
          onClick={onSendMoney}
          className="bg-white/20 hover:bg-white/30 text-white border-0 h-12 flex-col gap-1"
          variant="outline"
        >
          <Send className="h-4 w-4" />
          <span className="text-xs">Send</span>
        </Button>
        
        <Button
          onClick={onWithdraw}
          className="bg-white/20 hover:bg-white/30 text-white border-0 h-12 flex-col gap-1"
          variant="outline"
        >
          <ArrowDownLeft className="h-4 w-4" />
          <span className="text-xs">Withdraw</span>
        </Button>
      </div>
    </FintechCard>
  );
}