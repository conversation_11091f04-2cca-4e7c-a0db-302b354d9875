
import * as React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { 
  BarChart3, 
  CreditCard, 
  Home, 
  Settings, 
  Users,
  FileText,
  User,
  Shield,
  CreditCard as Card,
  Bell,
  HelpCircle,
  BookOpen,
  Clipboard,
} from "lucide-react";

interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  adminOnly?: boolean;
  userOnly?: boolean;
}

interface MainNavProps {
  isAdmin?: boolean;
  className?: string;
  collapsed?: boolean;
}

export function MainNav({ isAdmin = false, className, collapsed = false }: MainNavProps) {
  const location = useLocation();

  const navItems: NavItem[] = [
    {
      title: "Dashboard",
      href: isAdmin ? "/admin/dashboard" : "/dashboard",
      icon: <Home className="h-5 w-5" />,
    },
    {
      title: "Savings Plans",
      href: "/savings",
      icon: <Shield className="h-5 w-5" />,
      userOnly: true,
    },
    {
      title: "Payments",
      href: isAdmin ? "/admin/payment-management" : "/payments",
      icon: <CreditCard className="h-5 w-5" />,
    },
    {
      title: "Transactions",
      href: "/transactions",
      icon: <FileText className="h-5 w-5" />,
      userOnly: true,
    },
    {
      title: "Analytics",
      href: isAdmin ? "/admin/analytics" : "/analytics",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      title: "KYC Verification",
      href: "/kyc",
      icon: <Shield className="h-5 w-5" />,
      userOnly: true,
    },
    {
      title: "Settings",
      href: isAdmin ? "/admin/settings" : "/settings",
      icon: <Settings className="h-5 w-5" />,
    },
    // Admin-only navigation items
    {
      title: "User Management",
      href: "/admin/users",
      icon: <Users className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "Staff Management",
      href: "/admin/staff",
      icon: <User className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "Savings Plans",
      href: "/admin/group-savings",
      icon: <Clipboard className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "Withdrawal Requests",
      href: "/admin/requests",
      icon: <FileText className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "OTP Management",
      href: "/admin/verification",
      icon: <Shield className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "Assign Plans",
      href: "/admin/assign-plan",
      icon: <Card className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "Roles Management",
      href: "/admin/roles",
      icon: <Users className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "Staff Roles",
      href: "/admin/staff-roles",
      icon: <User className="h-5 w-5" />,
      adminOnly: true,
    },
    {
      title: "Notifications",
      href: "/admin/notifications",
      icon: <Bell className="h-5 w-5" />,
      adminOnly: true,
    },
  ];

  const filteredNavItems = navItems.filter(
    (item) => 
      (!item.adminOnly || (item.adminOnly && isAdmin)) && 
      (!item.userOnly || (item.userOnly && !isAdmin))
  );

  return (
    <nav className={cn("flex flex-col gap-1", className)}>
      {filteredNavItems.map((item) => {
        const isActive = location.pathname === item.href;
        return (
          <Link
            key={item.href}
            to={item.href}
            className={cn(
              "nav-link text-brand-yellow font-medium relative overflow-hidden transition-all duration-300",
              collapsed ? "justify-center px-2" : "",
              isActive 
                ? "active bg-brand-blue/70 before:absolute before:inset-0 before:bg-brand-yellow/10 before:animate-pulse-light" 
                : "hover:bg-brand-blue/30 hover:scale-105 hover:shadow-yellow/20"
            )}
            title={collapsed ? item.title : undefined}
          >
            <div className={cn(
              "transition-transform duration-300",
              isActive ? "scale-110" : "group-hover:scale-110"
            )}>
              {item.icon}
            </div>
            {!collapsed && (
              <span className={cn(
                "transition-all duration-300 font-sans tracking-wide",
                isActive ? "font-bold" : ""
              )}>
                {item.title}
              </span>
            )}
            {isActive && !collapsed && (
              <div className="absolute bottom-0 left-0 h-0.5 w-full bg-brand-yellow" />
            )}
          </Link>
        );
      })}
    </nav>
  );
}
