
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Check, Loader2 } from "lucide-react";

interface OtpVerificationProps {
  phoneNumber: string;
  onVerified: () => void;
  onCancel?: () => void;
}

export function OtpVerification({ phoneNumber, onVerified, onCancel }: OtpVerificationProps) {
  const { toast } = useToast();
  const [otpCode, setOtpCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  const handleVerify = () => {
    if (otpCode.length !== 6) {
      toast({
        title: "Invalid code",
        description: "Please enter a valid 6-digit code",
        variant: "destructive",
      });
      return;
    }

    setIsVerifying(true);
    
    // Simulate OTP verification
    setTimeout(() => {
      setIsVerifying(false);
      toast({
        title: "Verification successful",
        description: "Your phone number has been verified",
      });
      onVerified();
    }, 1500);
  };

  const handleResendOtp = () => {
    if (resendCooldown > 0) return;
    
    toast({
      title: "OTP sent",
      description: `A new code has been sent to ${phoneNumber}`,
    });
    
    // Start cooldown
    setResendCooldown(60);
    const interval = setInterval(() => {
      setResendCooldown((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="text-sm text-muted-foreground mb-1">
          Enter the 6-digit code sent to {phoneNumber}
        </div>
        <Input
          value={otpCode}
          onChange={(e) => setOtpCode(e.target.value.replace(/[^0-9]/g, "").slice(0, 6))}
          placeholder="Enter verification code"
          className="text-center text-lg tracking-widest"
          maxLength={6}
        />
      </div>
      
      <div className="flex gap-2">
        <Button
          onClick={handleVerify}
          className="flex-1 bg-brand-blue hover:bg-brand-blue/90"
          disabled={isVerifying || otpCode.length !== 6}
        >
          {isVerifying ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Verifying
            </>
          ) : (
            <>
              <Check className="mr-2 h-4 w-4" /> Verify
            </>
          )}
        </Button>
        {onCancel && (
          <Button onClick={onCancel} variant="outline" className="shrink-0">
            Cancel
          </Button>
        )}
      </div>
      
      <div className="text-center">
        <Button
          variant="link"
          size="sm"
          onClick={handleResendOtp}
          disabled={resendCooldown > 0}
          className="text-brand-blue"
        >
          {resendCooldown > 0 ? `Resend in ${resendCooldown}s` : "Resend code"}
        </Button>
      </div>
    </div>
  );
}
