const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');
const User = require('../models/user');

// POST /api/admin/impersonation/start - Start impersonating a user
router.post('/start', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId, reason } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    // Find the user to impersonate
    const targetUser = await User.findById(userId).select('-password');
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if user is active
    if (targetUser.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'Cannot impersonate inactive user'
      });
    }

    // Create impersonation session
    const impersonationSession = {
      originalUserId: req.user.id,
      originalUserEmail: req.user.email,
      originalUserName: `${req.user.firstName} ${req.user.lastName}`,
      impersonatedUserId: targetUser._id,
      impersonatedUserEmail: targetUser.email,
      impersonatedUserName: `${targetUser.firstName} ${targetUser.lastName}`,
      startedAt: new Date().toISOString(),
      reason: reason || 'Support'
    };

    // Create impersonation token
    const impersonationToken = jwt.sign(
      { 
        id: targetUser._id,
        email: targetUser.email,
        firstName: targetUser.firstName,
        lastName: targetUser.lastName,
        role: targetUser.role,
        isImpersonation: true,
        originalUserId: req.user.id,
        originalUserEmail: req.user.email,
        session: impersonationSession
      },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    // Log the impersonation action
    console.log(`Admin ${req.user.email} started impersonating user ${targetUser.email} for reason: ${reason}`);

    res.json({
      success: true,
      message: `Successfully started impersonating ${targetUser.firstName} ${targetUser.lastName}`,
      token: impersonationToken,
      session: impersonationSession
    });
  } catch (error) {
    console.error('Error starting impersonation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start impersonation'
    });
  }
});

// POST /api/admin/impersonation/stop - Stop impersonating a user
router.post('/stop', authenticateToken, async (req, res) => {
  try {
    // Check if this is an impersonation token
    if (!req.user.isImpersonation) {
      return res.status(400).json({
        success: false,
        message: 'Not currently impersonating a user'
      });
    }

    // Log the impersonation stop
    console.log(`Admin ${req.user.originalUserEmail} stopped impersonating user ${req.user.email}`);

    res.json({
      success: true,
      message: 'Impersonation stopped successfully'
    });
  } catch (error) {
    console.error('Error stopping impersonation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop impersonation'
    });
  }
});

// GET /api/admin/impersonation/current - Get current impersonation session
router.get('/current', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Check if currently impersonating
    if (req.user.isImpersonation) {
      res.json({
        success: true,
        session: {
          originalUserId: req.user.originalUserId,
          originalUserEmail: req.user.originalUserEmail,
          originalUserName: req.user.originalUserName,
          impersonatedUserId: req.user.id,
          impersonatedUserEmail: req.user.email,
          impersonatedUserName: `${req.user.firstName} ${req.user.lastName}`,
          startedAt: req.user.session?.startedAt,
          reason: req.user.session?.reason
        }
      });
    } else {
      res.json({
        success: true,
        session: null
      });
    }
  } catch (error) {
    console.error('Error getting current impersonation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get current impersonation'
    });
  }
});

module.exports = router; 