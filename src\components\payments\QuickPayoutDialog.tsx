
import { useState } from "react";
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON>Title,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { QuickPayout } from "@/components/payments/QuickPayout";
import { toast } from "sonner";
import { useBalance } from "@/hooks/use-balance";
import { Button } from "@/components/ui/button";
import { Wallet } from "lucide-react";

interface QuickPayoutDialogProps {
  onClose: () => void;
  onConfigureSettings: () => void;
}

export function QuickPayoutDialog({ onClose, onConfigureSettings }: QuickPayoutDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { updateBalance } = useBalance();
  
  // Check if payout settings exist
  const hasPayoutSettings = (): boolean => {
    const settings = localStorage.getItem('user_payout_settings');
    return !!settings;
  };

  const handlePayoutRequest = async (amount: number, reason: string) => {
    try {
      // Check if user has configured payout settings
      if (!hasPayoutSettings()) {
        toast.error("Please configure your payout settings first");
        onConfigureSettings();
        return;
      }
      
      setIsLoading(true);
      
      // Update balance (subtract the amount)
      const success = await updateBalance(amount, 'subtract');
      
      if (!success) {
        return;
      }
      
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create a transaction record in localStorage
      const transactions = JSON.parse(localStorage.getItem('user_transactions') || '[]');
      transactions.push({
        id: `txn-${Date.now()}`,
        type: 'withdrawal',
        amount: amount,
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        reference: `WIT${Math.floor(Math.random() * 100000)}`
      });
      localStorage.setItem('user_transactions', JSON.stringify(transactions));
      
      toast.success("Payout request submitted successfully");
      onClose();
    } catch (error) {
      console.error('Error processing payout:', error);
      toast.error('Failed to process payout request');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Request Payout</DialogTitle>
        <DialogDescription>
          Withdraw funds from your savings account
        </DialogDescription>
      </DialogHeader>
      
      {!hasPayoutSettings() ? (
        <div className="space-y-4 py-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-yellow-800">
            <p className="font-medium">No payout method configured</p>
            <p className="text-sm mt-1">You need to set up your payout method before you can request a withdrawal.</p>
          </div>
          <Button 
            onClick={onConfigureSettings}
            className="w-full"
          >
            <Wallet className="mr-2 h-4 w-4" />
            Configure Payout Settings
          </Button>
        </div>
      ) : (
        <QuickPayout
          onPayoutRequested={handlePayoutRequest}
          isLoading={isLoading}
        />
      )}
      
      <DialogFooter className="mt-4">
        {hasPayoutSettings() && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onConfigureSettings}
          >
            Change Payout Settings
          </Button>
        )}
      </DialogFooter>
    </DialogContent>
  );
}
