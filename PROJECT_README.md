
# ASUSU BY KOJA - System Architecture & Directory Structure

## Overview
ASUSU BY KOJA is a modern digital savings platform designed for Nigerians. The application provides a comprehensive solution for managing personal savings, with both user and administrative interfaces. The platform is built using modern web technologies with a focus on responsiveness, security, and user experience.

## Tech Stack

### Frontend
- **React**: JavaScript library for building user interfaces
- **TypeScript**: Typed superset of JavaScript
- **Vite**: Next-generation frontend tooling
- **React Router Dom**: For routing and navigation
- **TailwindCSS**: Utility-first CSS framework
- **Shadcn/UI**: Component library built on Radix UI
- **Lucide React**: Icon library
- **Recharts**: Charting library for React
- **React Hook Form**: Form validation library
- **Zod**: TypeScript-first schema validation
- **Sonner**: Toast notifications
- **Tanstack Query**: Data fetching and state management

## System Architecture

### Core Architecture Patterns

1. **Component-Based Architecture**
   - Reusable UI components for consistency
   - Separation of concerns through component hierarchy

2. **Client-Side Routing**
   - Single-page application (SPA) architecture
   - Route-based code organization

3. **Responsive Design**
   - Mobile-first approach
   - Adaptive layouts for different screen sizes

4. **User Authentication & Authorization**
   - Role-based access control (User vs Admin)
   - Secure authentication flows

## Directory Structure

```
┌── index.html                 # Main HTML entry point
├── README.md                  # Project information
├── vite.config.ts             # Vite configuration
├── tailwind.config.ts         # Tailwind CSS configuration
├── tsconfig.json              # TypeScript configuration
├── src/                       # Source code directory
│   ├── main.tsx               # Main application entry point
│   ├── App.tsx                # Root component with routing setup
│   ├── App.css                # Global styles
│   ├── index.css              # Entry CSS file
│   ├── components/            # Reusable components
│   │   ├── ui/                # Base UI components (shadcn/ui)
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── stat-card.tsx
│   │   │   └── ... (other UI components)
│   │   ├── layout/           # Layout components
│   │   │   ├── sidebar.tsx
│   │   │   └── user-layout.tsx
│   │   ├── analytics/        # Analytics-related components
│   │   │   ├── ActivityTimeline.tsx
│   │   │   ├── SavingsChart.tsx
│   │   │   └── ... (other analytics components)
│   │   ├── kyc/              # KYC verification components
│   │   ├── payments/         # Payment-related components
│   │   ├── plans/            # Savings plan components
│   │   ├── profile/          # User profile components
│   │   ├── main-nav.tsx      # Main navigation component
│   │   └── user-nav.tsx      # User navigation component
│   ├── hooks/                 # Custom React hooks
│   │   ├── use-mobile.tsx
│   │   └── use-toast.ts
│   ├── lib/                   # Utility libraries
│   │   └── utils.ts          # Common utility functions
│   └── pages/                 # Application pages
│       ├── Index.tsx         # Landing page
│       ├── Login.tsx         # User login page
│       ├── AdminLogin.tsx    # Admin login page
│       ├── Signup.tsx        # User registration page
│       ├── NotFound.tsx      # 404 page
│       ├── admin/            # Admin pages
│       │   ├── Dashboard.tsx
│       │   ├── UserManagement.tsx
│       │   ├── Analytics.tsx
│       │   ├── Requests.tsx
│       │   ├── SavingsPlansManagement.tsx
│       │   ├── PaymentManagement.tsx
│       │   ├── AdminRolesManagement.tsx
│       │   ├── AssignPlanToUser.tsx
│       │   ├── OtpVerificationManagement.tsx
│       │   └── UserProfileDetails.tsx
│       └── user/             # User pages
│           ├── Dashboard.tsx
│           ├── SavingsPlans.tsx
│           ├── Payments.tsx
│           ├── Transactions.tsx
│           ├── Analytics.tsx
│           ├── UserProfile.tsx
│           ├── Settings.tsx
│           ├── KycVerification.tsx
│           └── AddCard.tsx
└── public/                   # Static assets
    ├── favicon.ico
    └── lovable-uploads/      # Uploaded images
        └── 96b8da77-302c-4b65-87fb-ec3cf6bc86ca.png  # Koja Logo
```

## Key Features & Implementation

### 1. Authentication System
- Email/Phone based login
- Role-based access control (User vs Admin)
- Secure password management
- OTP verification for critical actions

### 2. User Dashboard
- Savings overview and analytics
- Plan management
- Transaction history
- Profile management

### 3. Admin Dashboard
- User management
- System analytics
- Request management
- Savings plan administration
- Security and compliance monitoring

### 4. Savings Management
- Multiple savings plan types
- Goal tracking
- Analytics and insights
- Rewards and challenges

### 5. Payment Processing
- Deposit methods
- Withdrawal requests
- Payment verification
- Transaction history

### 6. KYC Verification
- Identity verification
- Document upload
- Verification status tracking

## Component Architecture

### UI Component Hierarchy

The application follows a hierarchical component structure:

1. **Layout Components**
   - `UserLayout`: Main container for authenticated pages with sidebar and navigation
   - `Sidebar`: Navigation sidebar with collapsible functionality

2. **Page Components**
   - Admin pages (Dashboard, UserManagement, etc.)
   - User pages (Dashboard, SavingsPlans, etc.)

3. **Feature Components**
   - Analytics components
   - Payment components
   - KYC verification components
   - Profile management components

4. **Base UI Components**
   - Button, Card, Dialog, etc. (from shadcn/ui)
   - Custom styled components (StatCard, etc.)

## Styling System

The application uses Tailwind CSS with custom configurations:

- **Brand Colors**: 
  - `brand.blue`: #1231B8 (Koja blue)
  - `brand.yellow`: #FDE314 (Koja yellow)
  - `brand.deepBlue`: #0F1B82 (Darker shade)
  - `brand.lightBlue`: #E8EBFF (Lighter shade)

- **Custom Animations**:
  - Fade-in/out
  - Scale-in
  - Slide-in/out
  - Pulse effects
  - Yellow glow effects

- **Custom Shadows**:
  - Glass effect
  - Koja brand shadows
  - Yellow highlight shadows

## Routing Structure

The application uses React Router Dom for navigation with these main routes:

- `/`: Landing page
- `/login`: User login
- `/admin-login`: Admin login
- `/signup`: User registration
- `/dashboard`: User dashboard
- `/admin/dashboard`: Admin dashboard
- Various feature-specific routes under `/admin/` and user routes

## Future Development Considerations

1. **API Integration**
   - Connect to a full backend service for data persistence
   - Implement real-time updates using websockets

2. **Enhanced Security**
   - Two-factor authentication
   - Advanced fraud detection
   - Comprehensive audit logs

3. **Performance Optimization**
   - Code splitting for faster load times
   - Image optimization
   - Caching strategies

4. **Feature Expansion**
   - Mobile app development
   - Additional savings products
   - Community features and social savings
   - Financial education resources

## Development Guidelines

1. **Component Creation**
   - Create small, focused components (50 lines or less when possible)
   - Place new components in appropriate directory based on functionality
   - Follow existing naming conventions

2. **Styling**
   - Use Tailwind utility classes for styling
   - Maintain consistent use of brand colors
   - Ensure responsive design for all screen sizes

3. **State Management**
   - Use React Query for server state
   - Use React context for global UI state
   - Prefer local component state for component-specific concerns
