// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  password          String
  firstName         String
  lastName          String
  phoneNumber       String?
  role              Role     @default(USER)
  isVerified        Boolean  @default(false)
  isActive          Boolean  @default(true)
  balance           Float    @default(0)
  totalSavings      Float    @default(0)
  totalEarnings     Float    @default(0)
  lastLoginAt       DateTime?
  emailVerifiedAt   DateTime?
  phoneVerifiedAt   DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  savingsPlans      SavingsPlan[]
  transactions      Transaction[]
  notifications     Notification[]
  groupMemberships  GroupMembership[]
  kycDocuments      KYCDocument[]
  refreshTokens     RefreshToken[]

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model SavingsPlan {
  id              String            @id @default(cuid())
  userId          String
  name            String
  description     String?
  targetAmount    Float
  currentAmount   Float             @default(0)
  monthlyTarget   Float?
  startDate       DateTime
  endDate         DateTime?
  status          SavingsPlanStatus @default(ACTIVE)
  interestRate    Float             @default(0)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]

  @@map("savings_plans")
}

model Transaction {
  id            String          @id @default(cuid())
  userId        String
  savingsPlanId String?
  type          TransactionType
  amount        Float
  description   String?
  reference     String?         @unique
  status        TransactionStatus @default(PENDING)
  metadata      Json?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  savingsPlan SavingsPlan? @relation(fields: [savingsPlanId], references: [id], onDelete: SetNull)

  @@map("transactions")
}

model GroupSavings {
  id              String   @id @default(cuid())
  name            String
  description     String?
  totalMembers    Int
  contributionAmount Float
  frequency       GroupFrequency
  currentRound    Int      @default(1)
  totalRounds     Int
  status          GroupStatus @default(ACTIVE)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  memberships GroupMembership[]

  @@map("group_savings")
}

model GroupMembership {
  id            String   @id @default(cuid())
  userId        String
  groupId       String
  position      Int
  totalContributed Float @default(0)
  hasReceived   Boolean  @default(false)
  joinedAt      DateTime @default(now())

  user  User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  group GroupSavings @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@unique([userId, groupId])
  @@map("group_memberships")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType
  isRead    Boolean          @default(false)
  metadata  Json?
  createdAt DateTime         @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model KYCDocument {
  id           String    @id @default(cuid())
  userId       String
  documentType KYCDocumentType
  documentUrl  String
  status       KYCStatus @default(PENDING)
  rejectionReason String?
  verifiedAt   DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc_documents")
}

// Enums
enum Role {
  USER
  ADMIN
  SUPER_ADMIN
}

enum SavingsPlanStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  INTEREST
  FEE
  TRANSFER
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum GroupFrequency {
  WEEKLY
  MONTHLY
  QUARTERLY
}

enum GroupStatus {
  ACTIVE
  COMPLETED
  CANCELLED
}

enum NotificationType {
  TRANSACTION
  SAVINGS_GOAL
  GROUP_SAVINGS
  SYSTEM
  PROMOTION
}

enum KYCDocumentType {
  NATIONAL_ID
  PASSPORT
  DRIVERS_LICENSE
  UTILITY_BILL
  BANK_STATEMENT
}

enum KYCStatus {
  PENDING
  APPROVED
  REJECTED
}
