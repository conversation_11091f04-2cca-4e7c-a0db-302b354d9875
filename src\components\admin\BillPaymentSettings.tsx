import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Zap, Wifi, Phone, Car, Home, Save, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface BillProvider {
  id: string;
  name: string;
  category: string;
  fee: number;
  feeType: "fixed" | "percentage";
  isActive: boolean;
  icon: string;
}

const categoryIcons = {
  electricity: <Zap className="h-4 w-4" />,
  internet: <Wifi className="h-4 w-4" />,
  mobile: <Phone className="h-4 w-4" />,
  transport: <Car className="h-4 w-4" />,
  housing: <Home className="h-4 w-4" />
};

export const BillPaymentSettings = () => {
  const [providers, setProviders] = useState<BillProvider[]>([
    {
      id: "1",
      name: "EKEDC",
      category: "electricity",
      fee: 50,
      feeType: "fixed",
      isActive: true,
      icon: "electricity"
    },
    {
      id: "2",
      name: "MTN Nigeria",
      category: "mobile",
      fee: 1.5,
      feeType: "percentage",
      isActive: true,
      icon: "mobile"
    }
  ]);

  const [newProvider, setNewProvider] = useState({
    name: "",
    category: "",
    fee: "",
    feeType: "fixed" as "fixed" | "percentage"
  });

  const handleAddProvider = () => {
    if (!newProvider.name || !newProvider.category || !newProvider.fee) {
      toast.error("Please fill in all required fields");
      return;
    }

    const provider: BillProvider = {
      id: Date.now().toString(),
      name: newProvider.name,
      category: newProvider.category,
      fee: parseFloat(newProvider.fee),
      feeType: newProvider.feeType,
      isActive: true,
      icon: newProvider.category
    };

    setProviders([...providers, provider]);
    setNewProvider({
      name: "",
      category: "",
      fee: "",
      feeType: "fixed"
    });

    toast.success("Bill provider added successfully");
  };

  const toggleProviderStatus = (id: string) => {
    setProviders(providers.map(provider => 
      provider.id === id ? { ...provider, isActive: !provider.isActive } : provider
    ));
    toast.success("Provider status updated");
  };

  const deleteProvider = (id: string) => {
    setProviders(providers.filter(provider => provider.id !== id));
    toast.success("Provider deleted");
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Add Bill Payment Provider
          </CardTitle>
          <CardDescription>
            Configure bill payment providers and their fees
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="providerName">Provider Name</Label>
              <Input
                id="providerName"
                value={newProvider.name}
                onChange={(e) => setNewProvider(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., EKEDC, MTN Nigeria"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={newProvider.category} onValueChange={(value) => setNewProvider(prev => ({ ...prev, category: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="electricity">Electricity</SelectItem>
                  <SelectItem value="internet">Internet</SelectItem>
                  <SelectItem value="mobile">Mobile/Airtime</SelectItem>
                  <SelectItem value="transport">Transportation</SelectItem>
                  <SelectItem value="housing">Housing/Rent</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="feeType">Fee Type</Label>
              <Select value={newProvider.feeType} onValueChange={(value: "fixed" | "percentage") => setNewProvider(prev => ({ ...prev, feeType: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fixed">Fixed Amount (₦)</SelectItem>
                  <SelectItem value="percentage">Percentage (%)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fee">
                {newProvider.feeType === "fixed" ? "Fee Amount (₦)" : "Fee Percentage (%)"}
              </Label>
              <Input
                id="fee"
                type="number"
                step={newProvider.feeType === "percentage" ? "0.1" : "1"}
                value={newProvider.fee}
                onChange={(e) => setNewProvider(prev => ({ ...prev, fee: e.target.value }))}
                placeholder={newProvider.feeType === "fixed" ? "e.g., 50" : "e.g., 1.5"}
              />
            </div>
          </div>

          <Button onClick={handleAddProvider} className="gap-2">
            <Save className="h-4 w-4" />
            Add Provider
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Bill Payment Providers</CardTitle>
          <CardDescription>Manage available bill payment providers</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {providers.map((provider) => (
              <div key={provider.id} className="p-4 border rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {categoryIcons[provider.icon as keyof typeof categoryIcons]}
                    <h4 className="font-semibold">{provider.name}</h4>
                  </div>
                  <Badge variant={provider.isActive ? "default" : "secondary"}>
                    {provider.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  <p>Category: {provider.category}</p>
                  <p>Fee: {provider.feeType === "fixed" ? `₦${provider.fee}` : `${provider.fee}%`}</p>
                </div>

                <div className="flex items-center justify-between">
                  <Switch
                    checked={provider.isActive}
                    onCheckedChange={() => toggleProviderStatus(provider.id)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteProvider(provider.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};