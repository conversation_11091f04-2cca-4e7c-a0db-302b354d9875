const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/authMiddleware');
const SavingsPlan = require('../models/savingsPlan');
const Profit = require('../models/profit');
const Transaction = require('../models/transaction');
const User = require('../models/user');

// Get all interest rate plans
router.get('/rates', authenticateToken, async (req, res) => {
  try {
    const rates = await SavingsPlan.find({}).select('name description interestRate minimumAmount maximumAmount isActive category createdAt');
    
    // Transform data to match frontend expectations
    const transformedRates = rates.map(rate => ({
      id: rate._id,
      name: rate.name,
      description: rate.description,
      baseRate: rate.interestRate || 0,
      tier1Rate: rate.interestRate || 0,
      tier1Threshold: rate.minimumAmount || 0,
      tier2Rate: rate.interestRate || 0,
      tier2Threshold: rate.maximumAmount || 0,
      tier3Rate: rate.interestRate || 0,
      tier3Threshold: rate.maximumAmount || 0,
      compoundingFrequency: 'daily',
      minimumBalance: rate.minimumAmount || 0,
      maximumBalance: rate.maximumAmount || 0,
      isActive: rate.isActive !== false,
      planType: rate.category || 'savings',
      duration: rate.duration || 'flexible',
      earlyWithdrawalPenalty: 0,
      bonusRate: 0,
      bonusConditions: '',
      usersCount: 0, // Would need to calculate from user enrollments
      totalInterestPaid: 0, // Would need to calculate from profit records
      createdAt: rate.createdAt
    }));

    res.json(transformedRates);
  } catch (error) {
    console.error('Error fetching interest rates:', error);
    res.status(500).json({ error: 'Failed to fetch interest rates' });
  }
});

// Create new interest rate plan
router.post('/rates', authenticateToken, async (req, res) => {
  try {
    const {
      name,
      description,
      baseRate,
      minimumBalance,
      maximumBalance,
      isActive,
      planType,
      duration,
      earlyWithdrawalPenalty,
      bonusRate,
      bonusConditions
    } = req.body;

    const newRate = new SavingsPlan({
      name,
      description,
      interestRate: baseRate,
      minimumAmount: minimumBalance,
      maximumAmount: maximumBalance,
      isActive,
      category: planType,
      duration,
      earlyWithdrawalPenalty,
      bonusRate,
      bonusConditions
    });

    await newRate.save();
    res.status(201).json({ message: 'Interest rate plan created successfully', rate: newRate });
  } catch (error) {
    console.error('Error creating interest rate:', error);
    res.status(500).json({ error: 'Failed to create interest rate plan' });
  }
});

// Update interest rate plan
router.put('/rates/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const updatedRate = await SavingsPlan.findByIdAndUpdate(
      id,
      {
        name: updateData.name,
        description: updateData.description,
        interestRate: updateData.baseRate,
        minimumAmount: updateData.minimumBalance,
        maximumAmount: updateData.maximumBalance,
        isActive: updateData.isActive,
        category: updateData.planType,
        duration: updateData.duration,
        earlyWithdrawalPenalty: updateData.earlyWithdrawalPenalty,
        bonusRate: updateData.bonusRate,
        bonusConditions: updateData.bonusConditions
      },
      { new: true }
    );

    if (!updatedRate) {
      return res.status(404).json({ error: 'Interest rate plan not found' });
    }

    res.json({ message: 'Interest rate plan updated successfully', rate: updatedRate });
  } catch (error) {
    console.error('Error updating interest rate:', error);
    res.status(500).json({ error: 'Failed to update interest rate plan' });
  }
});

// Delete interest rate plan
router.delete('/rates/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const deletedRate = await SavingsPlan.findByIdAndDelete(id);

    if (!deletedRate) {
      return res.status(404).json({ error: 'Interest rate plan not found' });
    }

    res.json({ message: 'Interest rate plan deleted successfully' });
  } catch (error) {
    console.error('Error deleting interest rate:', error);
    res.status(500).json({ error: 'Failed to delete interest rate plan' });
  }
});

// Get interest calculations
router.get('/calculations', authenticateToken, async (req, res) => {
  try {
    const calculations = await Profit.find({})
      .populate('userId', 'name')
      .populate('savingsPlanId', 'name')
      .sort({ calculationDate: -1 })
      .limit(50);

    const transformedCalculations = calculations.map(calc => ({
      id: calc._id,
      userId: calc.userId._id,
      userName: calc.userId.name,
      planName: calc.savingsPlanId.name,
      balance: calc.amount * 20, // Mock balance calculation
      interestRate: calc.interestRate * 100,
      interestEarned: calc.amount,
      calculationDate: calc.calculationDate,
      status: calc.status
    }));

    res.json(transformedCalculations);
  } catch (error) {
    console.error('Error fetching interest calculations:', error);
    res.status(500).json({ error: 'Failed to fetch interest calculations' });
  }
});

// Calculate interest for a user
router.post('/calculate', authenticateToken, async (req, res) => {
  try {
    const { userId, savingsPlanId, interestRate } = req.body;

    // Find the savings plan
    const savingsPlan = await SavingsPlan.findById(savingsPlanId);
    if (!savingsPlan) {
      return res.status(404).json({ error: 'Savings plan not found' });
    }

    // Calculate interest (simplified calculation)
    const rate = interestRate || savingsPlan.interestRate || 0.05;
    const dailyRate = rate / 365;
    const profitAmount = (savingsPlan.minimumAmount || 1000) * dailyRate;

    // Create profit record
    const newProfit = new Profit({
      userId,
      savingsPlanId,
      amount: profitAmount,
      interestRate: rate,
      description: `Interest accrued at ${(rate * 100).toFixed(2)}% annual rate`,
      status: 'applied'
    });

    await newProfit.save();

    // Create transaction record
    const profitTransaction = new Transaction({
      userId,
      type: 'interest',
      amount: profitAmount,
      description: `Interest earned on ${savingsPlan.name}`,
      savingsPlanId,
      status: 'completed'
    });

    await profitTransaction.save();

    res.json({ 
      message: 'Interest calculated successfully',
      profit: newProfit,
      transaction: profitTransaction
    });
  } catch (error) {
    console.error('Error calculating interest:', error);
    res.status(500).json({ error: 'Failed to calculate interest' });
  }
});

// Get interest analytics
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    // Calculate total interest paid
    const totalInterest = await Profit.aggregate([
      { $match: { status: 'applied' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Calculate active plans
    const activePlans = await SavingsPlan.countDocuments({ isActive: true });

    // Calculate average interest rate
    const avgRate = await SavingsPlan.aggregate([
      { $group: { _id: null, average: { $avg: '$interestRate' } } }
    ]);

    // Calculate beneficiaries
    const beneficiaries = await Profit.distinct('userId');

    const analytics = {
      totalInterestPaid: totalInterest[0]?.total || 0,
      activePlans: activePlans,
      averageRate: avgRate[0]?.average || 0,
      beneficiaries: beneficiaries.length
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching interest analytics:', error);
    res.status(500).json({ error: 'Failed to fetch interest analytics' });
  }
});

module.exports = router; 