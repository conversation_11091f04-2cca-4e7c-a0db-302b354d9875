const express = require('express');
const router = express.Router();
const Account = require('../models/accounts');

// Create a new account
router.post('/create', async (req, res) => {
  try {
    const { accountName, accountType, accountNumber, bankName } = req.body;
    if (!accountName || !accountType || !accountNumber || !bankName) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    if (!/^[0-9]{10}$/.test(accountNumber)) {
      return res.status(400).json({ error: 'Account number must be 10 digits.' });
    }
    const account = new Account(req.body);
    const savedAccount = await account.save();
    res.status(201).json(savedAccount);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Update an existing account
router.put('/:id', async (req, res) => {
  try {
    const updatedAccount = await Account.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updatedAccount) {
      return res.status(404).json({ error: 'Account not found' });
    }
    res.json(updatedAccount);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Delete an account
router.delete('/:id', async (req, res) => {
  try {
    const deletedAccount = await Account.findByIdAndDelete(req.params.id);
    if (!deletedAccount) {
      return res.status(404).json({ error: 'Account not found' });
    }
    res.json({ message: 'Account deleted successfully' });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Route to retrieve all accounts
router.get('/all', async (req, res) => {
    try {
        const accounts = await Account.find();
        res.status(200).json(accounts);
    } catch (error) {
        res.status(500).json({ error: 'Failed to retrieve accounts' });
    }
});

module.exports = router;