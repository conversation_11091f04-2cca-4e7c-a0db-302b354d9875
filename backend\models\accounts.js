const mongoose = require('mongoose');

const accountSchema = new mongoose.Schema({
  bankName: {
    type: String,
    required: true,
  },
  accountName: {
    type: String,
    required: true,
  },
  accountNumber: {
    type: String,
    required: true,
    match: /^\d{10}$/, // Ensures the account number is exactly 10 digits
  },
  isActive: {
    type: Boolean,
    default: false, // Default value for the checkbox
  },
}, { timestamps: true }); // Adds createdAt and updatedAt fields

module.exports = mongoose.model('Account', accountSchema);