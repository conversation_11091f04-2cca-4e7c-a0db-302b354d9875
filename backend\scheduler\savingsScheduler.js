// backend/scheduler/savingsScheduler.js
// This script should be required in your main backend entry (e.g., index.js) to run automatically.

const cron = require('node-cron');
const SavingsPlan = require('../models/savingsPlan');
const GroupSavingsPlan = require('../models/groupSavingsPlan');
const RotationalGroupSavings = require('../models/rotationalGroupSavings');
const User = require('../models/user');
const Notification = require('../models/notification');
const Transaction = require('../models/transaction');
const TargetSavings = require('../models/targetSavings');
const Settings = require('../models/settings');

// Helper to get interest rate for a plan type (falls back to global)
async function getInterestRate(planType) {
  // Try plan-specific
  const planKey = planType ? `interestRate_${planType}` : null;
  let rate = null;
  if (planKey) {
    const planSetting = await Settings.findOne({ key: planKey });
    if (planSetting && typeof planSetting.value === 'number') {
      rate = planSetting.value;
    }
  }
  if (rate === null) {
    // Fallback to global
    const globalSetting = await Settings.findOne({ key: 'globalInterestRate' });
    if (globalSetting && typeof globalSetting.value === 'number') {
      rate = globalSetting.value;
    } else {
      rate = 12; // Default 12% if not set
    }
  }
  return rate;
}
// Helper to check if a deduction is due based on frequency and lastDepositDate
function isDue(plan, today) {
  // For target savings, make first deduction due only after one period from creation
  if (!plan.lastDepositDate) {
    // If this is a target savings plan, only due if enough time has passed since createdAt
    if (plan.frequency && plan.goalName !== undefined && plan.createdAt) {
      const created = new Date(plan.createdAt);
      if (isNaN(created.getTime())) return false;
      switch ((plan.depositFrequency || plan.frequency || '').toLowerCase()) {
        case 'daily':
          return today > new Date(created.setDate(created.getDate() + 1));
        case 'weekly':
          return today > new Date(created.setDate(created.getDate() + 7));
        case 'monthly':
          return today > new Date(created.setMonth(created.getMonth() + 1));
        case 'yearly':
          return today > new Date(created.setFullYear(created.getFullYear() + 1));
        default:
          return false;
      }
    }
    // For other plans, keep existing behavior
    return true;
  }
  const last = new Date(plan.lastDepositDate);
  if (isNaN(last.getTime())) {
    console.error(`[SavingsScheduler] Invalid lastDepositDate in isDue() for plan ${plan._id}:`, plan.lastDepositDate);
    return false; // Treat as not due, skip this plan
  }
  // Support both capitalized and lowercase frequency
  switch ((plan.depositFrequency || plan.frequency || '').toLowerCase()) {
    case 'daily':
      return today > new Date(last.setDate(last.getDate() + 1));
    case 'weekly':
      return today > new Date(last.setDate(last.getDate() + 7));
    case 'monthly':
      return today > new Date(last.setMonth(last.getMonth() + 1));
    case 'yearly':
      return today > new Date(last.setFullYear(last.getFullYear() + 1));
    default:
      return false;
  }
}

console.log('[SavingsScheduler] Module loaded. Waiting for cron schedule...');

// Defensive: Only schedule cron if system date is valid
const now = new Date();
if (!isNaN(now.getTime())) {
  // Run daily at midnight
  cron.schedule('0 0 * * *', async () => {
    console.log('[SavingsScheduler][CRON] Running scheduled job at', new Date().toISOString());
    const today = new Date();
    if (isNaN(today.getTime())) {
      return;
    }
    try {
      // INDIVIDUAL PLANS
      const plans = await SavingsPlan.find({});
      console.log(`[SavingsScheduler][INDIVIDUAL] Found ${plans.length} individual savings plans.`);
      for (const plan of plans) {
        console.log(`[SavingsScheduler][INDIVIDUAL] Processing plan:`, plan._id, plan.title || plan.name);
        // Only process if plan is active and not expired
        if (!plan.targetDate || isNaN(new Date(plan.targetDate).getTime())) { console.log(`[SavingsScheduler][INDIVIDUAL] Skipping plan ${plan._id}: invalid or missing targetDate`); continue; }
        if (plan.savedAmount >= plan.targetAmount) { console.log(`[SavingsScheduler][INDIVIDUAL] Skipping plan ${plan._id}: target reached`); continue; }
        if (today > new Date(plan.targetDate)) { console.log(`[SavingsScheduler][INDIVIDUAL] Skipping plan ${plan._id}: expired`); continue; }
        let user = await User.findById(plan.userId);
        if (!user) { console.log(`[SavingsScheduler][INDIVIDUAL] User not found for plan ${plan._id}`); continue; }
        let lastDepositDate = plan.lastDepositDate || plan.createdAt;
        let due = isDue({ ...plan.toObject(), lastDepositDate }, today);
        if (due && user.balance >= plan.depositAmount) {
          console.log(`[SavingsScheduler][INDIVIDUAL] Deducting ₦${plan.depositAmount} from user ${user._id} for plan ${plan._id}`);
          user.balance -= plan.depositAmount;
          plan.savedAmount += plan.depositAmount;
          plan.lastDepositDate = today;
          await user.save();
          await plan.save();
          await Transaction.create({
            date: today,
            description: `Auto deduction for plan '${plan.title || plan._id}'`,
            type: 'deposit',
            amount: plan.depositAmount,
            userId: user._id,
            balanceAfter: user.balance,
            reference: `IND-${plan._id}-${user._id}-${today.getTime()}`,
            createdAt: today,
          });
          await Notification.create({
            userId: user._id,
            type: 'savings_due',
            title: 'Savings Deducted',
            message: `₦${plan.depositAmount} was deducted for your savings plan '${plan.title || plan._id}'.`,
          });
        } else if (due) {
          console.log(`[SavingsScheduler][INDIVIDUAL] Insufficient balance for user ${user._id} (balance: ₦${user.balance}) for plan ${plan._id}. Needed: ₦${plan.depositAmount}`);
          await Notification.create({
            userId: user._id,
            type: 'savings_failed',
            title: 'Savings Deduction Failed',
            message: `We could not deduct ₦${plan.depositAmount} for your savings plan '${plan.title || plan._id}' due to insufficient balance.`,
          });
        }

        // --- INTEREST ACCRUAL FOR INDIVIDUAL PLANS ---
        // Only apply if plan is active and not expired
        if (!plan.lastInterestApplied || today > new Date(plan.lastInterestApplied).setMinutes(new Date(plan.lastInterestApplied).getMinutes() + 1)) {
          console.log(`[SavingsScheduler][INDIVIDUAL] Checking interest for plan ${plan._id}`);
          // Get interest rate for individual plans
          const rate = await getInterestRate('individual');
          // Interest is annual, apply monthly: (rate/100) / 12 * savedAmount
          const interest = (plan.savedAmount * (rate / 100)) / 12;
          console.log(`[SavingsScheduler][INDIVIDUAL] Interest calculation: savedAmount=${plan.savedAmount}, rate=${rate}, interest=${interest}`);
          if (interest > 0) {
            console.log(`[SavingsScheduler][INDIVIDUAL] Adding interest ₦${interest.toFixed(2)} to plan ${plan._id}`);
            plan.savedAmount += interest;
            plan.lastInterestApplied = today;
            await plan.save();
            await Transaction.create({
              date: today,
              description: `Interest accrued for plan '${plan.title || plan._id}'`,
              type: 'interest',
              amount: interest,
              userId: user._id,
              balanceAfter: user.balance,
              reference: `INT-IND-${plan._id}-${user._id}-${today.getTime()}`,
              createdAt: today,
            });
            await Notification.create({
              userId: user._id,
              type: 'interest',
              title: 'Interest Accrued',
              message: `₦${interest.toFixed(2)} interest was added to your savings plan '${plan.title || plan._id}'.`,
            });
          } else {
            console.log(`[SavingsScheduler][INDIVIDUAL] No interest accrued for plan ${plan._id} (interest=${interest})`);
          }
        }
      }

      // GROUP PLANS
      const groupPlans = await GroupSavingsPlan.find({});
      console.log(`[SavingsScheduler][GROUP] Found ${groupPlans.length} group savings plans.`);
      for (const plan of groupPlans) {
        console.log(`[SavingsScheduler][GROUP] Processing group plan:`, plan._id, plan.title || plan.name);
        // For each member, try to deduct their share
        for (const memberId of plan.members) {
          console.log(`[SavingsScheduler][GROUP] Processing member ${memberId} for plan ${plan._id}`);
          const user = await User.findById(memberId);
          if (!user) { console.log(`[SavingsScheduler][GROUP] User not found for member ${memberId} in plan ${plan._id}`); continue; }
          // Only process if plan is active and not expired
          if (!plan.targetDate || isNaN(new Date(plan.targetDate).getTime())) { console.log(`[SavingsScheduler][GROUP] Skipping plan ${plan._id}: invalid or missing targetDate`); continue; }
          if (plan.savedAmount >= plan.targetAmount) { console.log(`[SavingsScheduler][GROUP] Skipping plan ${plan._id}: target reached`); continue; }
          if (today > new Date(plan.targetDate)) { console.log(`[SavingsScheduler][GROUP] Skipping plan ${plan._id}: expired`); continue; }
          // Use plan.lastDepositDate for group, or fallback to plan.createdAt
          let lastDepositDate = plan.lastDepositDate || plan.createdAt;
          let due = isDue({ ...plan, lastDepositDate }, today);
          if (due && user.balance >= plan.depositAmount) {
            console.log(`[SavingsScheduler][GROUP] Deducting ₦${plan.depositAmount} from user ${user._id} for group plan ${plan._id}`);
            user.balance -= plan.depositAmount;
            plan.savedAmount += plan.depositAmount;
            plan.lastDepositDate = today;
            await user.save();
            await plan.save();
            await Transaction.create({
              date: today,
              description: `Auto-group deduction for plan '${plan.title || plan._id}'`,
              type: 'deposit',
              amount: plan.depositAmount,
              userId: user._id,
              balanceAfter: user.balance,
              reference: `GRP-${plan._id}-${user._id}-${today.getTime()}`,
              createdAt: today,
            });
            await Notification.create({
              userId: user._id,
              type: 'savings_due',
              title: 'Group Savings Deducted',
              message: `₦${plan.depositAmount} was deducted for your group savings plan '${plan.title || plan._id}'.`,
            });
          } else if (due) {
            console.log(`[SavingsScheduler][GROUP] Insufficient balance for user ${user._id} (balance: ₦${user.balance}) for group plan ${plan._id}. Needed: ₦${plan.depositAmount}`);
            await Notification.create({
              userId: user._id,
              type: 'savings_failed',
              title: 'Group Savings Deduction Failed',
              message: `We could not deduct ₦${plan.depositAmount} for your group savings plan '${plan.title || plan._id}' due to insufficient balance.`,
            });
          }

          // --- INTEREST ACCRUAL FOR GROUP PLANS (per member) ---
          // Only apply if plan is active and not expired
          if (!plan.lastInterestApplied || today > new Date(plan.lastInterestApplied).setMinutes(new Date(plan.lastInterestApplied).getMinutes() + 1)) {
            console.log(`[SavingsScheduler][GROUP] Checking interest for group plan ${plan._id}`);
            const rate = await getInterestRate('group');
            const interest = (plan.savedAmount * (rate / 100)) / 12;
            console.log(`[SavingsScheduler][GROUP] Interest calculation: savedAmount=${plan.savedAmount}, rate=${rate}, interest=${interest}`);
            if (interest > 0) {
              console.log(`[SavingsScheduler][GROUP] Adding interest ₦${interest.toFixed(2)} to group plan ${plan._id}`);
              plan.savedAmount += interest;
              plan.lastInterestApplied = today;
              await plan.save();
              await Transaction.create({
                date: today,
                description: `Interest accrued for group plan '${plan.title || plan._id}'`,
                type: 'interest',
                amount: interest,
                userId: user._id,
                balanceAfter: user.balance,
                reference: `INT-GRP-${plan._id}-${user._id}-${today.getTime()}`,
                createdAt: today,
              });
              await Notification.create({
                userId: user._id,
                type: 'interest',
                title: 'Interest Accrued',
                message: `₦${interest.toFixed(2)} interest was added to your group savings plan '${plan.title || plan._id}'.`,
              });
            } else {
              console.log(`[SavingsScheduler][GROUP] No interest accrued for group plan ${plan._id} (interest=${interest})`);
            }
          }
        }
      }

      // TARGET SAVINGS PLANS
      const targetPlans = await TargetSavings.find({});
      console.log(`[SavingsScheduler][TARGET] Found ${targetPlans.length} target savings plans.`);
      for (const plan of targetPlans) {
        console.log(`[SavingsScheduler][TARGET] Processing target plan:`, plan._id, plan.goalName || plan.title || plan.name);
        // console.log(`[SavingsScheduler][Target] Processing plan:`, plan);
        // DEBUG: Log all deduction attempts for this plan
        if (!plan.lastDepositDate) {
          // console.log(`[SavingsScheduler][Target][DEBUG] No lastDepositDate for plan ${plan._id}. First deduction is due.`);
        } else {
          // console.log(`[SavingsScheduler][Target][DEBUG] lastDepositDate for plan ${plan._id}:`, plan.lastDepositDate);
        }
        // Only process if plan is active and not expired (no end date, so just check target not reached)
        if (plan.savedAmount >= plan.targetAmount) { console.log(`[SavingsScheduler][TARGET] Skipping plan ${plan._id}: target reached`); continue; }
        let user = await User.findById(plan.userId);
        if (!user) { console.log(`[SavingsScheduler][TARGET] User not found for plan ${plan._id}`); continue; }
        // For target, use frequency and timelineMonths
        // Only use lastDepositDate for due check; if undefined, first deduction is due immediately
        let lastDepositDate = plan.lastDepositDate;
        let due = isDue({ ...plan.toObject(), lastDepositDate, frequency: plan.frequency }, today);
        // Calculate per-period amount
        const periods = plan.timelineMonths || 1;
        const perPeriodAmount = Math.ceil(plan.targetAmount / periods);
        if (due && user.balance >= perPeriodAmount) {
          console.log(`[SavingsScheduler][TARGET] Deducting ₦${perPeriodAmount} from user ${user._id} for target plan ${plan._id}`);
          user.balance -= perPeriodAmount;
          plan.savedAmount = (plan.savedAmount || 0) + perPeriodAmount;
          // Patch: Persist lastDepositDate update using updateOne to ensure DB write
          const newLastDepositDate = today;
          try {
            await TargetSavings.updateOne(
              { _id: plan._id },
              { $set: { lastDepositDate: newLastDepositDate } }
            );
            plan.lastDepositDate = newLastDepositDate; // update in-memory for rest of logic
          } catch (err) {
            console.error(`[SavingsScheduler][Target][PATCH][ERROR] Failed to persist lastDepositDate for plan ${plan._id}:`, err);
          }
          await user.save();
          await plan.save();
          await Transaction.create({
            date: today,
            description: `Auto deduction for target savings '${plan.goalName || plan._id}'`,
            type: 'deposit',
            amount: perPeriodAmount,
            userId: user._id,
            balanceAfter: user.balance,
            reference: `TGT-${plan._id}-${user._id}-${today.getTime()}`,
            createdAt: today,
          });
          await Notification.create({
            userId: user._id,
            type: 'savings_due',
            title: 'Target Savings Deducted',
            message: `₦${perPeriodAmount} was deducted for your target savings '${plan.goalName || plan._id}'.`,
          });
        } else if (due) {
          console.log(`[SavingsScheduler][TARGET] Insufficient balance for user ${user._id} (balance: ₦${user.balance}) for target plan ${plan._id}. Needed: ₦${perPeriodAmount}`);
          await Notification.create({
            userId: user._id,
            type: 'savings_failed',
            title: 'Target Savings Deduction Failed',
            message: `We could not deduct ₦${perPeriodAmount} for your target savings '${plan.goalName || plan._id}' due to insufficient balance.`,
          });
        }

        // --- INTEREST ACCRUAL FOR TARGET SAVINGS ---
        if (!plan.lastInterestApplied || today > new Date(plan.lastInterestApplied).setMinutes(new Date(plan.lastInterestApplied).getMinutes() + 1)) {
          console.log(`[SavingsScheduler][TARGET] Checking interest for target plan ${plan._id}`);
          const rate = await getInterestRate('target');
          const interest = (plan.savedAmount * (rate / 100)) / 12;
          console.log(`[SavingsScheduler][TARGET] Interest calculation: savedAmount=${plan.savedAmount}, rate=${rate}, interest=${interest}`);
          if (interest > 0) {
            console.log(`[SavingsScheduler][TARGET] Adding interest ₦${interest.toFixed(2)} to target plan ${plan._id}`);
            plan.savedAmount += interest;
            plan.lastInterestApplied = today;
            await plan.save();
            await Transaction.create({
              date: today,
              description: `Interest accrued for target savings '${plan.goalName || plan._id}'`,
              type: 'interest',
              amount: interest,
              userId: user._id,
              balanceAfter: user.balance,
              reference: `INT-TGT-${plan._id}-${user._id}-${today.getTime()}`,
              createdAt: today,
            });
            await Notification.create({
              userId: user._id,
              type: 'interest',
              title: 'Interest Accrued',
              message: `₦${interest.toFixed(2)} interest was added to your target savings '${plan.goalName || plan._id}'.`,
            });
          } else {
            console.log(`[SavingsScheduler][TARGET] No interest accrued for target plan ${plan._id} (interest=${interest})`);
          }
        }
      }

      // ROTATIONAL GROUP PLANS
      const rotationalPlans = await RotationalGroupSavings.find({ isActive: true });
      console.log(`[SavingsScheduler][ROTATIONAL] Found ${rotationalPlans.length} rotational group savings plans.`);
      for (const plan of rotationalPlans) {
        console.log(`[SavingsScheduler][ROTATIONAL] Processing rotational plan:`, plan._id, plan.name);
        // Only process if nextPayoutDate is due
        if (!plan.nextPayoutDate || isNaN(new Date(plan.nextPayoutDate).getTime())) continue;
        if (today < new Date(plan.nextPayoutDate)) continue;
        // For each member who hasn't paid, try to deduct
        for (const member of plan.members) {
          console.log(`[SavingsScheduler][ROTATIONAL] Processing member ${member.userId} for rotational plan ${plan._id}`);
          if (member.hasPaid) continue;
          const user = await User.findById(member.userId);
          if (!user) { console.log(`[SavingsScheduler][ROTATIONAL] User not found for member ${member.userId} in plan ${plan._id}`); continue; }
          if (user.balance >= plan.amountPerInterval) {
            console.log(`[SavingsScheduler][ROTATIONAL] Deducting ₦${plan.amountPerInterval} from user ${user._id} for rotational plan ${plan._id}`);
            user.balance -= plan.amountPerInterval;
            member.hasPaid = true;
            await user.save();
            await plan.save();
            await Transaction.create({
              date: today,
              description: `Auto-rotational deduction for group '${plan.name || plan._id}'`,
              type: 'deposit',
              amount: plan.amountPerInterval,
              userId: user._id,
              balanceAfter: user.balance,
              reference: `ROT-${plan._id}-${user._id}-${today.getTime()}`,
              createdAt: today,
            });
            await Notification.create({
              userId: user._id,
              type: 'savings_due',
              title: 'Rotational Savings Deducted',
              message: `₦${plan.amountPerInterval} was deducted for your rotational group '${plan.name || plan._id}'.`,
            });
            console.log(`[SavingsScheduler][ROTATIONAL] Deducted ₦${plan.amountPerInterval} from user ${user._id} for rotational group ${plan._id}. New balance: ₦${user.balance}`);
          } else {
            console.log(`[SavingsScheduler][ROTATIONAL] Insufficient balance for user ${user._id} (balance: ₦${user.balance}) for rotational plan ${plan._id}. Needed: ₦${plan.amountPerInterval}`);
            await Notification.create({
              userId: user._id,
              type: 'savings_failed',
              title: 'Rotational Savings Deduction Failed',
              message: `We could not deduct ₦${plan.amountPerInterval} for your rotational group '${plan.name || plan._id}' due to insufficient balance.`,
            });
          }
        }
      }

      console.log(`[SavingsScheduler][CRON] Processed all savings plans at ${today}`);
    } catch (err) {
      console.error('[SavingsScheduler] Error processing savings plans:', err);
    }
  });
} else {
  console.error('[SavingsScheduler] System date is invalid. Cron job not scheduled.');
}

// Export for testing or manual run
module.exports = {};
