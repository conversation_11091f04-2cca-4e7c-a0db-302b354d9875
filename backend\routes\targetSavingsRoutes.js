const express = require('express');
const router = express.Router();
const TargetSavings = require('../models/targetSavings');
const { authenticateToken } = require('../middleware/authMiddleware');

// Create a new target savings goal
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { goalName, targetAmount, timelineMonths, frequency } = req.body;
    if (!goalName || !targetAmount || !timelineMonths || !frequency) {
      return res.status(400).json({ error: 'All fields are required.' });
    }
    const newGoal = new TargetSavings({
      userId,
      goalName,
      targetAmount,
      timelineMonths,
      frequency
    });
    const savedGoal = await newGoal.save();
    res.status(201).json(savedGoal);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create target savings goal', details: error.message });
  }
});

// Get all target savings goals for the logged-in user
router.get('/', authenticateToken, async (req, res) => {
  const { getInterestRate } = require('../utils/interestRateUtil');
  const Transaction = require('../models/transaction');
  try {
    const userId = req.user.id;
    const goals = await TargetSavings.find({ userId }).sort({ createdAt: -1 });
    // Add currentAmount, interestAccrued, and interestRate to each target goal
    for (let i = 0; i < goals.length; i++) {
      const goal = goals[i];
      const goalObj = goal.toObject();
      goalObj.currentAmount = goalObj.savedAmount || 0;
      goalObj.progress = goalObj.targetAmount > 0 ? Math.round((goalObj.currentAmount / goalObj.targetAmount) * 100) : 0;
      // Calculate total interest accrued for this goal
      const interestTxs = await Transaction.find({ userId, type: 'interest', description: { $regex: goal._id.toString() } });
      goalObj.interestAccrued = interestTxs.reduce((sum, tx) => sum + (tx.amount || 0), 0);
      // Add interest rate for this goal (target)
      goalObj.interestRate = await getInterestRate('target');
      goals[i] = goalObj;
    }
    res.status(200).json(goals);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch target savings goals', details: error.message });
  }
});

// Update a target savings goal
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const updatedGoal = await TargetSavings.findOneAndUpdate(
      { _id: id, userId },
      req.body,
      { new: true }
    );
    if (!updatedGoal) {
      return res.status(404).json({ error: 'Target savings goal not found.' });
    }
    res.status(200).json(updatedGoal);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update target savings goal', details: error.message });
  }
});

// Delete a target savings goal
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const deleted = await TargetSavings.findOneAndDelete({ _id: id, userId });
    if (!deleted) {
      return res.status(404).json({ error: 'Target savings goal not found.' });
    }
    res.status(200).json({ message: 'Target savings goal deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete target savings goal', details: error.message });
  }
});

module.exports = router;
