
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Bell, Check } from "lucide-react";
import { useNotifications } from "@/hooks/use-notifications";
import { NotificationTester } from "@/components/notification/notification-tester";

export function NotificationSettings() {
  const { toast } = useToast();
  const { addNotification } = useNotifications();
  const [settings, setSettings] = useState({
    savingsAlerts: true,
    withdrawalAlerts: true,
    depositAlerts: true,
    promotionalMessages: false,
    weeklyReports: true,
    securityAlerts: true,
    smsNotifications: true,
    emailNotifications: true,
    appNotifications: true,
  });
  const [loading, setLoading] = useState(false);

  const handleToggle = (setting: keyof typeof settings) => {
    setSettings((prev) => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  const handleSave = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      
      // Show toast
      toast({
        title: "Settings updated",
        description: "Your notification preferences have been saved",
      });
      
      // Send a notification
      addNotification({
        title: "Notification Settings Updated",
        message: "Your notification preferences have been successfully updated.",
        type: "success",
        channel: "in-app",
      });
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center font-unica-one tracking-wider">
            <Bell className="h-5 w-5 mr-2" />
            NOTIFICATION SETTINGS
          </CardTitle>
          <CardDescription>
            Choose what notifications you receive and how they are delivered
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-unica-one mb-3 text-brand-blue tracking-wide">NOTIFICATION TYPES</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Savings Alerts</Label>
                    <p className="text-sm text-muted-foreground">Notifications when your savings plan matures</p>
                  </div>
                  <Switch
                    checked={settings.savingsAlerts}
                    onCheckedChange={() => handleToggle('savingsAlerts')}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Withdrawal Alerts</Label>
                    <p className="text-sm text-muted-foreground">Notifications when money is withdrawn</p>
                  </div>
                  <Switch
                    checked={settings.withdrawalAlerts}
                    onCheckedChange={() => handleToggle('withdrawalAlerts')}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Deposit Alerts</Label>
                    <p className="text-sm text-muted-foreground">Notifications when money is deposited</p>
                  </div>
                  <Switch
                    checked={settings.depositAlerts}
                    onCheckedChange={() => handleToggle('depositAlerts')}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Promotional Messages</Label>
                    <p className="text-sm text-muted-foreground">Updates about new features and offers</p>
                  </div>
                  <Switch
                    checked={settings.promotionalMessages}
                    onCheckedChange={() => handleToggle('promotionalMessages')}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Weekly Reports</Label>
                    <p className="text-sm text-muted-foreground">Weekly summary of your savings activities</p>
                  </div>
                  <Switch
                    checked={settings.weeklyReports}
                    onCheckedChange={() => handleToggle('weeklyReports')}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Security Alerts</Label>
                    <p className="text-sm text-muted-foreground">Notifications about security events</p>
                  </div>
                  <Switch
                    checked={settings.securityAlerts}
                    onCheckedChange={() => handleToggle('securityAlerts')}
                  />
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-unica-one mb-3 text-brand-blue tracking-wide">DELIVERY CHANNELS</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive notifications via SMS</p>
                  </div>
                  <Switch
                    checked={settings.smsNotifications}
                    onCheckedChange={() => handleToggle('smsNotifications')}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                  </div>
                  <Switch
                    checked={settings.emailNotifications}
                    onCheckedChange={() => handleToggle('emailNotifications')}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">App Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive in-app notifications</p>
                  </div>
                  <Switch
                    checked={settings.appNotifications}
                    onCheckedChange={() => handleToggle('appNotifications')}
                  />
                </div>
              </div>
            </div>
          </div>
          
          <Button 
            onClick={handleSave} 
            className="bg-brand-blue hover:bg-brand-blue/90 font-unica-one tracking-wide"
            disabled={loading}
          >
            {loading ? (
              "SAVING..."
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                SAVE PREFERENCES
              </>
            )}
          </Button>
        </CardContent>
      </Card>
      
      <NotificationTester />
    </div>
  );
}
