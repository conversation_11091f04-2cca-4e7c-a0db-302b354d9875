import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Search, UserPlus, Edit, Briefcase } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { CreateUserForm } from "@/components/admin/CreateUserForm";
import { getAllUsers } from "@/integrations/supabase/admin-users";
import { Link } from "react-router-dom";

interface StaffMember {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  avatar_url: string | null;
  status: 'active' | 'suspended' | 'blocked';
  department?: string;
  position?: string;
  isAdmin: boolean;
  roles?: Array<{ role: string }> | null;
}

const StaffManagement = () => {
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<StaffMember[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const fetchStaff = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await getAllUsers();
      
      if (error) throw error;
      
      if (data) {
        const staffMembers: StaffMember[] = data.map(user => ({
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          phone: user.phone || '',
          avatar_url: user.avatar_url,
          status: user.status || 'active',
          department: user.department || 'General',
          position: user.position || 'Staff',
          isAdmin: user.isAdmin || false,
          roles: user.roles || []
        }));
        
        setStaff(staffMembers);
        setFilteredStaff(staffMembers);
      }
    } catch (error) {
      console.error("Error fetching staff:", error);
      toast.error("Failed to load staff members");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStaff();
  }, []);

  useEffect(() => {
    if (searchTerm) {
      const filtered = staff.filter(
        member =>
          member.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredStaff(filtered);
    } else {
      setFilteredStaff(staff);
    }
  }, [searchTerm, staff]);

  const handleUserCreated = () => {
    setCreateDialogOpen(false);
    fetchStaff();
    toast.success("Staff member added successfully");
  };

  const getRoleDisplay = (member: StaffMember) => {
    if (member.isAdmin) {
      return "Admin";
    }
    if (Array.isArray(member.roles) && member.roles.length > 0) {
      const nonUserRole = member.roles.find(r => r.role !== 'user');
      if (nonUserRole) {
        return nonUserRole.role.charAt(0).toUpperCase() + nonUserRole.role.slice(1);
      }
    }
    return "User";
  };

  const getDepartment = (member: StaffMember) => {
    return member.department || "General";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Staff Management</h2>
          <p className="text-muted-foreground">
            Manage staff members and their roles
          </p>
        </div>
        
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-brand-blue text-white">
              <UserPlus className="mr-2 h-4 w-4" /> Add Staff
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add New Staff Member</DialogTitle>
            </DialogHeader>
            <CreateUserForm onSuccess={handleUserCreated} />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Staff Directory</CardTitle>
          <CardDescription>
            View and manage all staff members
          </CardDescription>
          <div className="flex items-center gap-2 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search staff..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList>
              <TabsTrigger value="all">All Staff</TabsTrigger>
              <TabsTrigger value="admin">Admins</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="suspended">Suspended</TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="mt-4">
              {renderStaffTable(filteredStaff, isLoading)}
            </TabsContent>
            <TabsContent value="admin" className="mt-4">
              {renderStaffTable(filteredStaff.filter(m => m.isAdmin), isLoading)}
            </TabsContent>
            <TabsContent value="active" className="mt-4">
              {renderStaffTable(filteredStaff.filter(m => m.status === 'active'), isLoading)}
            </TabsContent>
            <TabsContent value="suspended" className="mt-4">
              {renderStaffTable(filteredStaff.filter(m => m.status === 'suspended'), isLoading)}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );

  function renderStaffTable(staffList: StaffMember[], loading: boolean) {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-brand-blue border-solid"></div>
        </div>
      );
    }
    
    if (staffList.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          No staff members found.
        </div>
      );
    }
    
    return (
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Staff Member</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {staffList.map((member) => (
              <TableRow key={member.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      {member.avatar_url ? (
                        <img
                          src={member.avatar_url}
                          alt={`${member.first_name} ${member.last_name}`}
                          className="object-cover"
                        />
                      ) : (
                        <AvatarFallback>
                          {member.first_name[0]}
                          {member.last_name[0]}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div>
                      <div className="font-medium">
                        {member.first_name} {member.last_name}
                      </div>
                      <div className="text-xs text-muted-foreground">{member.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={member.isAdmin ? "default" : "outline"}>
                    {getRoleDisplay(member)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Briefcase className="h-4 w-4 text-muted-foreground" />
                    <span>{getDepartment(member)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      member.status === "active"
                        ? "success"
                        : member.status === "suspended"
                        ? "warning"
                        : "destructive"
                    }
                  >
                    {member.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    asChild
                  >
                    <Link to={`/admin/users/${member.id}`}>
                      <Edit className="h-4 w-4 mr-2" /> Edit
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }
};

export default StaffManagement;
