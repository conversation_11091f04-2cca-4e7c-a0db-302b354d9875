import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Shield, Lock, AlertTriangle } from 'lucide-react';

const SafeLockForm = () => {
  const [amount, setAmount] = useState('');
  const [duration, setDuration] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const rates = {
    1: 18,
    3: 20,
    6: 22,
    12: 25
  };

  const calculateReturns = () => {
    if (!amount || !duration) return 0;
    const principal = parseFloat(amount);
    const rate = rates[Number(duration) as keyof typeof rates] / 100;
    const months = parseInt(duration);
    return principal * rate * (months / 12);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "SafeLock Created!",
        description: `₦${parseFloat(amount).toLocaleString()} locked for ${duration} months at premium rates.`,
      });
      
      setAmount('');
      setDuration('');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create SafeLock. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const returns = calculateReturns();

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-full bg-amber-100 dark:bg-amber-900/20 flex items-center justify-center">
          <Shield className="h-5 w-5 text-amber-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">SafeLock</h2>
          <p className="text-sm text-muted-foreground">Highest returns, complete lock</p>
        </div>
      </div>

      <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-2 mb-2">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <span className="text-sm font-medium text-amber-600">Important Notice</span>
        </div>
        <p className="text-xs text-amber-600">
          Funds are completely locked until maturity. Early withdrawal incurs 50% penalty on principal.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <FloatingLabelInput
          id="amount"
          type="number"
          label="Amount (₦)"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          required
          min="50000"
          placeholder="50,000"
        />

        <div className="space-y-2">
          <label className="text-sm font-medium">Lock Duration</label>
          <Select value={duration} onValueChange={setDuration}>
            <SelectTrigger>
              <SelectValue placeholder="Select duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1 month (18% p.a.)</SelectItem>
              <SelectItem value="3">3 months (20% p.a.)</SelectItem>
              <SelectItem value="6">6 months (22% p.a.)</SelectItem>
              <SelectItem value="12">12 months (25% p.a.)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {amount && duration && (
          <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Lock className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Premium Returns</span>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Principal:</span>
                <span>₦{parseFloat(amount).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Interest:</span>
                <span className="text-primary font-medium">₦{returns.toLocaleString()}</span>
              </div>
              <div className="flex justify-between font-semibold pt-2 border-t">
                <span>Total at Maturity:</span>
                <span>₦{(parseFloat(amount) + returns).toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}

        <Button 
          type="submit" 
          className="w-full" 
          disabled={loading || !amount || !duration}
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <span className="animate-spin mr-2">⟳</span> Creating SafeLock...
            </span>
          ) : (
            <>
              <Shield className="mr-2 h-4 w-4" />
              Create SafeLock
            </>
          )}
        </Button>
      </form>
    </FintechCard>
  );
};

export default SafeLockForm;